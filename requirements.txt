# Main Requirements File
# ====================
# This file contains all production dependencies for the GPT Trader system.
# For development dependencies, see requirements-dev.txt
# For exact version locks, see requirements-pinned.txt
# 
# To install: pip install -r requirements.txt
# To upgrade: pip install --upgrade -r requirements.txt

# Core dependencies
pydantic>=2.0.0
pydantic-settings>=2.0.0
python-dotenv>=1.0.0
MetaTrader5>=5.0.45
openai>=1.0.0
tiktoken>=0.5.0
holidays==0.19

# Data processing
pandas>=2.0.0
numpy>=1.24.0

# Machine learning
scikit-learn>=1.3.0
xgboost>=2.0.0
joblib>=1.3.0
imbalanced-learn>=0.11.0  # Note: was 'imblearn' - using canonical name

# Multi-threaded Gradient Boosting (GPU-accelerated)
catboost>=1.2.0  # Fast multi-threaded gradient boosting with GPU support

# Deep learning (GPU-accelerated)
torch>=2.1.0
torchvision>=0.15.0  # For PyTorch GPU support
torchaudio>=2.0.0    # For PyTorch GPU support
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0

# Note: For CUDA GPU support, install PyTorch with:
# pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Visualization and dashboards
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0
streamlit>=1.28.0
mplfinance>=0.12.9b7
tabulate>=0.9.0

# Technical analysis
ta>=0.10.0

# Async and networking
aiohttp>=3.9.0
requests>=2.31.0
backoff>=2.2.0
nest-asyncio>=1.5.0  # Note: was 'nest_asyncio' - using canonical name

# Database
aiosqlite>=0.19.0

# Utilities
schedule>=1.2.0
scipy>=1.11.0
psutil>=5.9.0
h5py>=3.10.0
lightgbm>=4.6.0
ta-lib>=0.6.4
lightgbm>=4.6.0