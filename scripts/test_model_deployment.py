#!/usr/bin/env python3
"""
Test model deployment validation
"""

import numpy as np
import pandas as pd
import logging
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_pipeline_predict():
    """Test that a scikit-learn Pipeline has predict method"""
    logger.info("Testing Pipeline predict method...")
    
    # Create a simple pipeline
    pipeline = Pipeline([
        ('scaler', StandardScaler()),
        ('classifier', RandomForestClassifier(n_estimators=10, random_state=42))
    ])
    
    # Create dummy data
    X = np.random.randn(100, 5)
    y = np.random.choice([-1, 0, 1], size=100)
    
    # Train pipeline
    pipeline.fit(X, y)
    
    # Test predict method
    if hasattr(pipeline, 'predict'):
        logger.info("✓ Pipeline has predict method")
        
        # Test prediction
        test_X = np.random.randn(10, 5)
        predictions = pipeline.predict(test_X)
        logger.info(f"✓ Pipeline prediction works: {predictions[:5]}")
        
        return True
    else:
        logger.error("✗ Pipeline missing predict method")
        return False

def test_model_package_structure():
    """Test model package structure"""
    logger.info("Testing model package structure...")
    
    # Create a pipeline
    pipeline = Pipeline([
        ('scaler', StandardScaler()),
        ('classifier', RandomForestClassifier(n_estimators=10, random_state=42))
    ])
    
    # Train it
    X = np.random.randn(100, 5)
    y = np.random.choice([-1, 0, 1], size=100)
    pipeline.fit(X, y)
    
    # Create model package (old format)
    model_package = {
        'pipeline': pipeline,
        'feature_names': [f'feature_{i}' for i in range(5)],
        'symbol': 'TEST',
        'performance': {'accuracy': 0.75}
    }
    
    logger.info(f"Model package type: {type(model_package)}")
    logger.info(f"Pipeline type: {type(model_package['pipeline'])}")
    
    # Test direct model access
    if hasattr(pipeline, 'predict'):
        logger.info("✓ Direct pipeline has predict method")
    else:
        logger.error("✗ Direct pipeline missing predict method")
    
    # Test package access
    if isinstance(model_package, dict) and 'pipeline' in model_package:
        extracted_model = model_package['pipeline']
        if hasattr(extracted_model, 'predict'):
            logger.info("✓ Extracted pipeline has predict method")
            return True
        else:
            logger.error("✗ Extracted pipeline missing predict method")
            return False
    else:
        logger.error("✗ Model package structure invalid")
        return False

def test_validation_logic():
    """Test the validation logic"""
    logger.info("Testing validation logic...")
    
    # Create a pipeline
    pipeline = Pipeline([
        ('scaler', StandardScaler()),
        ('classifier', RandomForestClassifier(n_estimators=10, random_state=42))
    ])
    
    # Train it
    X = np.random.randn(100, 5)
    y = np.random.choice([-1, 0, 1], size=100)
    pipeline.fit(X, y)
    
    # Test validation logic (simulate what the service does)
    def validate_model(model):
        logger.info(f"Validating model type: {type(model)}")
        
        # Check model has predict method
        if not hasattr(model, 'predict'):
            # If it's a dictionary (old format), try to get the pipeline
            if isinstance(model, dict) and 'pipeline' in model:
                logger.warning("Model is a dictionary, extracting pipeline...")
                model = model['pipeline']
                if not hasattr(model, 'predict'):
                    logger.error(f"Pipeline missing predict method. Type: {type(model)}")
                    return False
            else:
                logger.error(f"Model missing predict method. Type: {type(model)}")
                return False
        
        # Test prediction
        try:
            dummy_features = np.zeros((1, 5))
            _ = model.predict(dummy_features)
            logger.info("✓ Model prediction test passed")
            return True
        except Exception as e:
            logger.error(f"Model prediction test failed: {e}")
            return False
    
    # Test with direct pipeline
    logger.info("Testing direct pipeline...")
    result1 = validate_model(pipeline)
    
    # Test with model package
    logger.info("Testing model package...")
    model_package = {'pipeline': pipeline, 'feature_names': ['f1', 'f2', 'f3', 'f4', 'f5']}
    result2 = validate_model(model_package)
    
    return result1 and result2

def main():
    logger.info("="*60)
    logger.info("MODEL DEPLOYMENT VALIDATION TEST")
    logger.info("="*60)
    
    success = True
    
    # Test pipeline predict method
    success &= test_pipeline_predict()
    
    # Test model package structure
    success &= test_model_package_structure()
    
    # Test validation logic
    success &= test_validation_logic()
    
    logger.info("\n" + "="*60)
    if success:
        logger.info("✅ ALL TESTS PASSED!")
        logger.info("Model deployment validation should now work correctly")
    else:
        logger.error("❌ SOME TESTS FAILED!")
        logger.error("Model deployment validation needs more work")
    logger.info("="*60)

if __name__ == "__main__":
    main()
