#!/usr/bin/env python3
"""
Test SentenceTransformer loading with progress tracking
"""

import logging
import sys
import time
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_sentence_transformer_progress():
    """Test SentenceTransformer loading with progress tracking"""
    try:
        logger.info("Testing SentenceTransformer loading with progress tracking...")
        
        # Import the memory service
        from core.services.memory_service import EmbeddingGenerator
        
        # Test with a small model first
        model_name = "sentence-transformers/all-MiniLM-L6-v2"
        
        logger.info(f"Creating EmbeddingGenerator for model: {model_name}")
        
        # Create the embedding generator (this will trigger the loading)
        start_time = time.time()
        
        embedding_gen = EmbeddingGenerator(
            model_name=model_name,
            cache_dir="./cache/huggingface"
        )
        
        total_time = time.time() - start_time
        
        logger.info(f"Total loading time: {total_time:.1f} seconds")
        
        # Test that the model works
        logger.info("Testing model functionality...")
        test_text = "This is a test sentence for embedding generation."
        embedding = embedding_gen.generate_embedding(test_text)
        
        logger.info(f"Generated embedding shape: {embedding.shape}")
        logger.info(f"Embedding sample: {embedding[:5]}")
        
        logger.info("✅ SentenceTransformer loading with progress tracking works!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_progress_tracker_only():
    """Test just the progress tracker without actual model loading"""
    try:
        logger.info("Testing progress tracker...")
        
        from core.services.memory_service import ModelLoadingProgress
        
        # Simulate model loading progress
        progress = ModelLoadingProgress("test-model")
        
        steps = [
            "Initializing",
            "Checking cache", 
            "Downloading files",
            "Loading tokenizer",
            "Loading model",
            "Moving to GPU",
            "Finalizing"
        ]
        
        for step in steps:
            progress.update_step(step)
            time.sleep(0.5)  # Simulate work
        
        progress.complete()
        
        logger.info("✅ Progress tracker works!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Progress tracker test failed: {e}")
        return False

def main():
    logger.info("="*60)
    logger.info("SENTENCETRANSFORMER PROGRESS TRACKING TEST")
    logger.info("="*60)
    
    # Test progress tracker first
    logger.info("\n1. Testing progress tracker...")
    progress_ok = test_progress_tracker_only()
    
    if progress_ok:
        # Test actual model loading
        logger.info("\n2. Testing SentenceTransformer loading...")
        model_ok = test_sentence_transformer_progress()
    else:
        model_ok = False
    
    logger.info("\n" + "="*60)
    if progress_ok and model_ok:
        logger.info("✅ ALL TESTS PASSED!")
        logger.info("SentenceTransformer will now load with progress tracking")
    else:
        logger.error("❌ SOME TESTS FAILED!")
        if not progress_ok:
            logger.error("Progress tracker failed")
        if not model_ok:
            logger.error("Model loading failed")
    logger.info("="*60)

if __name__ == "__main__":
    main()
