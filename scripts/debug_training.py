#!/usr/bin/env python3
"""
Debug wrapper for ML training to catch silent exits
"""

import sys
import traceback
import logging
import asyncio
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('debug_training.log')
    ]
)

logger = logging.getLogger(__name__)

def exception_handler(exc_type, exc_value, exc_traceback):
    """Global exception handler to catch unhandled exceptions"""
    if issubclass(exc_type, KeyboardInterrupt):
        logger.info("KeyboardInterrupt received - exiting gracefully")
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger.error("Uncaught exception occurred!")
    logger.error(f"Exception type: {exc_type.__name__}")
    logger.error(f"Exception value: {exc_value}")
    logger.error("Traceback:")
    logger.error(''.join(traceback.format_exception(exc_type, exc_value, exc_traceback)))

# Set global exception handler
sys.excepthook = exception_handler

async def debug_main():
    """Debug wrapper for main training function"""
    try:
        logger.info("="*80)
        logger.info("DEBUG TRAINING WRAPPER STARTED")
        logger.info("="*80)
        
        # Import and run the main training
        logger.info("Importing training module...")
        from scripts.train_ml_production import main as training_main
        
        logger.info("Starting training main function...")
        await training_main()
        
        logger.info("Training completed successfully!")
        
    except ImportError as e:
        logger.error(f"Import error: {e}")
        logger.error("This might be due to missing dependencies or configuration")
        traceback.print_exc()
        
    except Exception as e:
        logger.error(f"Training failed with exception: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        traceback.print_exc()
        
    except SystemExit as e:
        logger.error(f"SystemExit called with code: {e.code}")
        traceback.print_exc()
        
    finally:
        logger.info("Debug wrapper finished")

def main():
    """Main debug function"""
    try:
        logger.info("Starting debug training wrapper...")
        asyncio.run(debug_main())
    except Exception as e:
        logger.error(f"Asyncio run failed: {e}")
        traceback.print_exc()
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    finally:
        logger.info("Debug wrapper exiting")

if __name__ == "__main__":
    main()
