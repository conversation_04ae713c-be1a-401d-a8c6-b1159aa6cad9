#!/usr/bin/env python3
"""
Production ML Training System with GPU Support
Learns from actual trading patterns and market conditions
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import sys
import os
from typing import Dict, List, Tuple, Optional, Any, Union
import json
import pickle
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# ML imports
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import (
    classification_report, confusion_matrix, f1_score,
    precision_score, recall_score, roc_auc_score, make_scorer,
    accuracy_score
)
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.pipeline import Pipeline
from sklearn.base import BaseEstimator, TransformerMixin
import lightgbm as lgb
import joblib

# GPU/CUDA support imports
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# Project imports
from core.infrastructure.data.unified_data_provider import UnifiedDataProvider, DataRequest
from core.infrastructure.database.repositories import TradeRepository, SignalRepository
from core.infrastructure.database.backtest_repository import BacktestRepository
from core.services.model_management_service import ModelManagementService, ModelRepository
from core.infrastructure.mt5.data_provider import MT5DataProvider
from core.infrastructure.mt5.client import MT5Client
from core.domain.enums.mt5_enums import TimeFrame
from core.domain.models import TradeResult
from core.utils.chart_utils import ChartGenerator
from config.settings import get_settings
from config.symbols import get_symbols_by_group

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Log PyTorch availability after logger is configured
if TORCH_AVAILABLE:
    logger.info(f"PyTorch available. CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        logger.info(f"CUDA devices: {torch.cuda.device_count()}")
        logger.info(f"Current device: {torch.cuda.get_device_name()}")
else:
    logger.warning("PyTorch not available. Install with: pip install torch")


def detect_gpu_capabilities():
    """Detect available GPU capabilities for ML training"""
    gpu_info = {
        'cuda_available': False,
        'device': 'cpu',
        'device_name': 'CPU',
        'lightgbm_gpu': False,
        'torch_gpu': False
    }

    # Check CUDA availability
    if TORCH_AVAILABLE and torch.cuda.is_available():
        gpu_info['cuda_available'] = True
        gpu_info['device'] = 'cuda'
        gpu_info['device_name'] = torch.cuda.get_device_name()
        gpu_info['torch_gpu'] = True
        logger.info(f"✓ CUDA detected: {gpu_info['device_name']}")

    # Check LightGBM GPU support
    try:
        # Test if LightGBM can use GPU
        test_lgb = lgb.LGBMClassifier(device='gpu', gpu_platform_id=0, gpu_device_id=0, verbosity=-1)
        gpu_info['lightgbm_gpu'] = True
        logger.info("✓ LightGBM GPU support available")
    except Exception as e:
        logger.info(f"LightGBM GPU not available: {e}")

    if not gpu_info['cuda_available']:
        logger.info("Using CPU for ML training")

    return gpu_info


class TorchNeuralNetwork(nn.Module):
    """PyTorch neural network for trading signal prediction"""

    def __init__(self, input_size: int, hidden_sizes: List[int] = [128, 64, 32], num_classes: int = 3, dropout: float = 0.3):
        super(TorchNeuralNetwork, self).__init__()

        layers = []
        prev_size = input_size

        for hidden_size in hidden_sizes:
            layers.extend([
                nn.Linear(prev_size, hidden_size),
                nn.BatchNorm1d(hidden_size),
                nn.ReLU(),
                nn.Dropout(dropout)
            ])
            prev_size = hidden_size

        # Output layer
        layers.append(nn.Linear(prev_size, num_classes))

        self.network = nn.Sequential(*layers)

    def forward(self, x):
        return self.network(x)


class TorchTrainer:
    """PyTorch model trainer with GPU support"""

    def __init__(self, device: str = 'cpu'):
        self.device = device

    def train_model(self, X_train, y_train, X_val, y_val, epochs: int = 100, batch_size: int = 256):
        """Train PyTorch model"""
        input_size = X_train.shape[1]
        model = TorchNeuralNetwork(input_size).to(self.device)

        # Convert labels from {-1, 0, 1} to {0, 1, 2} for PyTorch
        # -1 (SELL) -> 0, 0 (WAIT) -> 1, 1 (BUY) -> 2
        y_train_pytorch = y_train + 1  # Convert -1,0,1 to 0,1,2
        y_val_pytorch = y_val + 1

        # Validate label ranges
        assert y_train_pytorch.min() >= 0, f"Invalid train labels: min={y_train_pytorch.min()}"
        assert y_train_pytorch.max() <= 2, f"Invalid train labels: max={y_train_pytorch.max()}"
        assert y_val_pytorch.min() >= 0, f"Invalid val labels: min={y_val_pytorch.min()}"
        assert y_val_pytorch.max() <= 2, f"Invalid val labels: max={y_val_pytorch.max()}"

        logger.info(f"Label distribution - Train: {y_train_pytorch.value_counts().to_dict()}")
        logger.info(f"Label distribution - Val: {y_val_pytorch.value_counts().to_dict()}")

        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train.values).to(self.device)
        y_train_tensor = torch.LongTensor(y_train_pytorch.values).to(self.device)
        X_val_tensor = torch.FloatTensor(X_val.values).to(self.device)
        y_val_tensor = torch.LongTensor(y_val_pytorch.values).to(self.device)

        # Create data loaders
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

        # Optimizer and loss
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
        criterion = nn.CrossEntropyLoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)

        best_val_acc = 0
        patience_counter = 0

        for epoch in range(epochs):
            # Training
            model.train()
            train_loss = 0
            for batch_X, batch_y in train_loader:
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                train_loss += loss.item()

            # Validation
            model.eval()
            with torch.no_grad():
                val_outputs = model(X_val_tensor)
                val_loss = criterion(val_outputs, y_val_tensor)
                val_pred = torch.argmax(val_outputs, dim=1)
                val_acc = (val_pred == y_val_tensor).float().mean().item()

            scheduler.step(val_loss)

            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= 20:  # Early stopping
                logger.info(f"Early stopping at epoch {epoch}")
                break

            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch}: Train Loss: {train_loss/len(train_loader):.4f}, Val Acc: {val_acc:.4f}")

        return model, best_val_acc


from core.ml.feature_engineering import ProductionFeatureEngineer


@dataclass
class TradingExample:
    """Represents a historical trading example"""
    timestamp: datetime
    symbol: str
    features: Dict[str, float]
    signal: str  # BUY/SELL/WAIT
    outcome: Optional[TradeResult] = None
    profit_pips: Optional[float] = None
    risk_reward: Optional[float] = None


class TradingPatternLearner:
    """Learns from successful trading patterns in historical data with GPU support"""

    def __init__(self):
        self.settings = get_settings()
        self.setup_infrastructure()
        self.feature_engineer = ProductionFeatureEngineer()

        # Detect GPU capabilities
        self.gpu_info = detect_gpu_capabilities()
        self.device = self.gpu_info['device']

        # Initialize PyTorch trainer if available
        if TORCH_AVAILABLE:
            self.torch_trainer = TorchTrainer(self.device)
        else:
            self.torch_trainer = None
        
    def setup_infrastructure(self):
        """Initialize all required services"""
        db_path = self.settings.database.db_path
        self.trade_repository = TradeRepository(db_path)
        self.signal_repository = SignalRepository(db_path)
        self.backtest_repository = BacktestRepository(db_path)
        self.model_repository = ModelRepository(db_path)
        
        self.mt5_client = MT5Client(self.settings.mt5)
        self.data_provider = UnifiedDataProvider(mt5_client=self.mt5_client)
        
        self.model_management = ModelManagementService(
            models_dir=Path("models"),
            model_repository=self.model_repository
        )
    
    async def learn_from_patterns(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime,
        training_metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Learn from successful trading patterns"""
        
        logger.info(f"Learning trading patterns for {symbol}")
        
        try:
            # 1. Fetch comprehensive market data
            df = await self._fetch_comprehensive_data(symbol, start_date, end_date)
            if df is None or len(df) < 500:
                return {'status': 'error', 'error': 'Insufficient data'}
            
            # 2. Create sophisticated labels based on tradeable opportunities
            labels, metadata = self._create_trading_labels(df)
            
            logger.info(f"Created labels with distribution: {labels.value_counts().to_dict()}")
            logger.info(f"Metadata: {metadata}")
            
            # 3. Engineer features
            features = self.feature_engineer.transform(df)
            feature_names = self.feature_engineer.feature_names
            
            # 4. Align data
            min_length = min(len(features), len(labels))
            features = features[-min_length:]
            labels = labels.iloc[-min_length:]
            
            # 5. Remove samples with NaN labels
            valid_indices = ~labels.isna()
            features = features[valid_indices]
            labels = labels[valid_indices]
            
            X = pd.DataFrame(features, columns=feature_names)
            y = labels.astype(int)
            
            logger.info(f"Training on {len(X)} samples with {len(feature_names)} features")
            
            # 6. Train advanced models
            best_model, performance = await self._train_advanced_models(X, y, symbol, training_metadata)
            
            # 7. Save if performance meets criteria
            if self._meets_deployment_criteria(performance):
                model_metadata = await self._deploy_model(
                    model=best_model,
                    symbol=symbol,
                    performance=performance,
                    feature_names=feature_names
                )
                
                return {
                    'status': 'success',
                    'model_id': model_metadata['model_id'],
                    'performance': performance,
                    'label_metadata': metadata
                }
            else:
                return {
                    'status': 'rejected',
                    'performance': performance,
                    'reason': 'Does not meet deployment criteria'
                }
                
        except Exception as e:
            logger.error(f"Error learning patterns for {symbol}: {e}", exc_info=True)
            return {'status': 'error', 'error': str(e)}
    
    async def _fetch_comprehensive_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime
    ) -> Optional[pd.DataFrame]:
        """Fetch comprehensive market data"""
        
        request = DataRequest(
            symbol=symbol,
            timeframe=TimeFrame.H1,
            start_date=start_date,
            end_date=end_date
        )
        
        market_data = await self.data_provider.get_data(request)
        if not market_data:
            return None
        
        # Convert to DataFrame
        data = []
        for candle in market_data.candles:
            data.append({
                'timestamp': candle.timestamp,
                'open': candle.open,
                'high': candle.high,
                'low': candle.low,
                'close': candle.close,
                'volume': candle.volume
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        # Add spread estimation (important for trading)
        df['spread'] = (df['high'] - df['low']) * 0.1  # Rough estimate
        
        return df
    
    def _create_trading_labels(self, df: pd.DataFrame) -> Tuple[pd.Series, Dict]:
        """Create labels based on actual tradeable opportunities"""
        
        labels = pd.Series(index=df.index, dtype=float)
        
        # Calculate key levels
        atr = self.feature_engineer._calculate_atr(df, 14)
        
        # Look for profitable trading opportunities
        for i in range(100, len(df) - 50):  # Need history and future
            current_idx = df.index[i]
            
            # Skip if ATR is too low (no volatility)
            if atr.iloc[i] < df['close'].iloc[i] * 0.0003:  # Reduced to 3 pips minimum
                labels.iloc[i] = 0
                continue
            
            # Check future price movement
            future_prices = df['close'].iloc[i+1:i+25]  # Next 24 hours
            entry_price = df['close'].iloc[i]
            
            # More flexible profit targets for better balance
            # Option 1: Conservative targets (1.5:1 RR)
            long_target_1 = entry_price + (atr.iloc[i] * 1.5)
            long_stop_1 = entry_price - (atr.iloc[i] * 1)
            
            # Option 2: Moderate targets (1:1 RR) 
            long_target_2 = entry_price + (atr.iloc[i] * 1)
            long_stop_2 = entry_price - (atr.iloc[i] * 1)
            
            # Option 3: Scalping targets (0.5 ATR profit)
            long_target_3 = entry_price + (atr.iloc[i] * 0.5)
            long_stop_3 = entry_price - (atr.iloc[i] * 0.5)
            
            short_target_1 = entry_price - (atr.iloc[i] * 1.5)
            short_stop_1 = entry_price + (atr.iloc[i] * 1)
            
            short_target_2 = entry_price - (atr.iloc[i] * 1)
            short_stop_2 = entry_price + (atr.iloc[i] * 1)
            
            short_target_3 = entry_price - (atr.iloc[i] * 0.5)
            short_stop_3 = entry_price + (atr.iloc[i] * 0.5)
            
            # Check multiple profit scenarios
            long_profitable = False
            short_profitable = False
            
            # Check each target level
            for target, stop in [(long_target_1, long_stop_1), 
                                (long_target_2, long_stop_2),
                                (long_target_3, long_stop_3)]:
                hit_target = any(future_prices >= target)
                hit_stop = any(future_prices <= stop)
                
                if hit_target and hit_stop:
                    target_idx = future_prices[future_prices >= target].index[0]
                    stop_idx = future_prices[future_prices <= stop].index[0]
                    if target_idx < stop_idx:
                        long_profitable = True
                        break
                elif hit_target and not hit_stop:
                    long_profitable = True
                    break
            
            for target, stop in [(short_target_1, short_stop_1),
                                (short_target_2, short_stop_2), 
                                (short_target_3, short_stop_3)]:
                hit_target = any(future_prices <= target)
                hit_stop = any(future_prices >= stop)
                
                if hit_target and hit_stop:
                    target_idx = future_prices[future_prices <= target].index[0]
                    stop_idx = future_prices[future_prices >= stop].index[0]
                    if target_idx < stop_idx:
                        short_profitable = True
                        break
                elif hit_target and not hit_stop:
                    short_profitable = True
                    break
            
            # Relaxed filters for better balance
            # 1. Trend alignment (more flexible)
            sma_20 = df['close'].rolling(20).mean().iloc[i]
            sma_50 = df['close'].rolling(50).mean().iloc[i]
            
            # Allow trades in ranging markets too
            trend_up = entry_price > sma_20
            trend_down = entry_price < sma_20
            strong_trend_up = entry_price > sma_20 > sma_50
            strong_trend_down = entry_price < sma_20 < sma_50
            
            # 2. Recent momentum (less strict)
            momentum_5 = (entry_price - df['close'].iloc[i-5]) / df['close'].iloc[i-5]
            momentum_10 = (entry_price - df['close'].iloc[i-10]) / df['close'].iloc[i-10]
            
            # 3. Volatility condition
            recent_volatility = df['close'].iloc[i-20:i].pct_change().std()
            # Calculate historical volatility over past 50 periods
            historical_vol = df['close'].iloc[max(0, i-50):i].pct_change().std()
            is_volatile = recent_volatility > historical_vol * 1.2  # 20% above historical average
            
            # Label assignment with relaxed filters
            if long_profitable:
                # Strong setup
                if strong_trend_up and momentum_5 > 0.001:
                    labels.iloc[i] = 1
                # Moderate setup
                elif trend_up and (momentum_5 > 0 or momentum_10 > 0.002):
                    labels.iloc[i] = 1
                # Volatility breakout setup
                elif is_volatile and momentum_5 > 0.0005:
                    labels.iloc[i] = 1
                else:
                    labels.iloc[i] = 0
                    
            elif short_profitable:
                # Strong setup
                if strong_trend_down and momentum_5 < -0.001:
                    labels.iloc[i] = 2
                # Moderate setup  
                elif trend_down and (momentum_5 < 0 or momentum_10 < -0.002):
                    labels.iloc[i] = 2
                # Volatility breakout setup
                elif is_volatile and momentum_5 < -0.0005:
                    labels.iloc[i] = 2
                else:
                    labels.iloc[i] = 0
            else:
                labels.iloc[i] = 0
        
        # For multi-class classification: 0 = WAIT, 1 = BUY, -1 = SELL
        labels_for_training = labels.copy()
        labels_for_training[labels_for_training == 2] = -1  # Convert SELL from 2 to -1
        
        metadata = {
            'total_samples': len(labels),
            'buy_signals': (labels == 1).sum(),
            'sell_signals': (labels == 2).sum(),
            'no_signals': (labels == 0).sum(),
            'trade_ratio': (labels > 0).sum() / len(labels)
        }
        
        return labels_for_training, metadata
    
    async def _train_advanced_models(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        symbol: str,
        training_metadata: Dict[str, Any] = None
    ) -> Tuple[Any, Dict]:
        """Train advanced models with proper methodology"""
        
        # Time series split
        n_splits = 5
        tscv = TimeSeriesSplit(n_splits=n_splits)
        
        # Calculate class weights for imbalanced data (multi-class)
        from sklearn.utils.class_weight import compute_class_weight
        classes = np.unique(y)
        class_weights = compute_class_weight('balanced', classes=classes, y=y)
        class_weight_dict = dict(zip(classes, class_weights))
        
        logger.info(f"Class distribution: {y.value_counts().to_dict()}")
        logger.info(f"Class weights: {class_weight_dict}")
        
        # Models to evaluate with GPU support
        models = {}

        # LightGBM with GPU support if available
        lgb_params = {
            'n_estimators': 300,  # Increased for GPU training
            'learning_rate': 0.05,  # Reduced for better learning
            'num_leaves': 31,  # Increased for GPU
            'max_depth': 7,  # Increased depth for GPU
            'min_child_samples': 20,  # Reduced for more complex patterns
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'reg_alpha': 0.3,
            'reg_lambda': 0.3,
            'random_state': 42,
            'importance_type': 'gain',
            'class_weight': class_weight_dict,
            'objective': 'multiclass',
            'num_class': 3,
            'verbosity': -1
        }

        if self.gpu_info['lightgbm_gpu']:
            lgb_params.update({
                'device': 'gpu',
                'gpu_platform_id': 0,
                'gpu_device_id': 0,
                'n_jobs': 1  # GPU doesn't need multiple jobs
            })
            logger.info("✓ Using LightGBM with GPU acceleration")
        else:
            lgb_params['n_jobs'] = -1
            logger.info("Using LightGBM with CPU")

        models['lgb'] = lgb.LGBMClassifier(**lgb_params)
        # Traditional ML models - optimized for speed with large datasets
        models['rf'] = RandomForestClassifier(
            n_estimators=200,  # Reduced for faster training with large dataset
            max_depth=10,      # Reduced depth for speed
            min_samples_split=50,  # Increased to reduce overfitting and speed up
            min_samples_leaf=20,   # Increased for speed
            max_features='sqrt',
            random_state=42,
            n_jobs=-1,
            class_weight='balanced',
            max_samples=0.8    # Use only 80% of samples per tree for speed
        )

        models['gb'] = GradientBoostingClassifier(
            n_estimators=150,  # Reduced for speed with large dataset
            learning_rate=0.1,  # Increased for faster convergence
            max_depth=6,       # Reduced for speed
            min_samples_split=50,  # Increased for speed
            min_samples_leaf=20,   # Increased for speed
            subsample=0.7,     # Reduced for speed
            random_state=42
        )
        
        # Create pipeline with scaling
        best_score = 0
        best_model = None
        best_model_name = None
        
        logger.info(f"Training models for {symbol} with {n_splits}-fold time series CV...")

        # Split data for PyTorch validation
        from sklearn.model_selection import train_test_split
        X_train_torch, X_val_torch, y_train_torch, y_val_torch = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        # Train PyTorch model if available
        if TORCH_AVAILABLE and self.torch_trainer:
            logger.info("Training PyTorch neural network...")
            logger.info(f"Original label distribution: {y.value_counts().to_dict()}")
            logger.info(f"Label range: {y.min()} to {y.max()}")
            try:
                torch_model, torch_val_acc = self.torch_trainer.train_model(
                    X_train_torch, y_train_torch, X_val_torch, y_val_torch
                )

                # Evaluate PyTorch model
                torch_model.eval()
                with torch.no_grad():
                    X_test_tensor = torch.FloatTensor(X_val_torch.values).to(self.device)
                    torch_outputs = torch_model(X_test_tensor)
                    torch_pred_pytorch = torch.argmax(torch_outputs, dim=1).cpu().numpy()

                    # Convert predictions back from {0, 1, 2} to {-1, 0, 1}
                    torch_pred = torch_pred_pytorch - 1  # Convert 0,1,2 back to -1,0,1

                torch_precision = precision_score(y_val_torch, torch_pred, average='weighted', zero_division=0)
                torch_f1 = f1_score(y_val_torch, torch_pred, average='weighted', zero_division=0)
                torch_score = 0.7 * torch_precision + 0.3 * torch_f1

                logger.info(f"PyTorch NN - Val Acc: {torch_val_acc:.4f}, Precision: {torch_precision:.4f}, F1: {torch_f1:.4f}")

                if torch_score > best_score:
                    best_score = torch_score
                    best_model = torch_model
                    best_model_name = 'pytorch_nn'

            except Exception as e:
                error_msg = str(e)
                if "CUDA error" in error_msg or "device-side assert" in error_msg:
                    logger.error(f"PyTorch CUDA error: {e}")
                    logger.error("This is likely due to invalid label values. Check label preprocessing.")
                    logger.info("Falling back to CPU training for PyTorch...")
                    # Try CPU fallback
                    try:
                        cpu_trainer = TorchTrainer('cpu')
                        torch_model, torch_val_acc = cpu_trainer.train_model(
                            X_train_torch, y_train_torch, X_val_torch, y_val_torch
                        )
                        logger.info("PyTorch CPU fallback successful")
                    except Exception as cpu_e:
                        logger.warning(f"PyTorch CPU fallback also failed: {cpu_e}")
                else:
                    logger.warning(f"PyTorch training failed: {e}")

        # Train traditional ML models with progress tracking
        for name, model in models.items():
            logger.info(f"Training {name} model...")
            start_time = datetime.now()

            pipeline = Pipeline([
                ('scaler', RobustScaler()),
                ('classifier', model)
            ])

            # Custom scoring for trading
            def trading_score(y_true, y_pred):
                # Prioritize precision over recall for trading
                # Use weighted average for multi-class
                precision = precision_score(y_true, y_pred, average='weighted', zero_division=0)
                recall = recall_score(y_true, y_pred, average='weighted', zero_division=0)
                f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0)

                # Custom score that values precision more
                return 0.7 * precision + 0.3 * f1

            scorer = make_scorer(trading_score)

            try:
                # Cross-validation with progress tracking
                cv_scores = []
                fold_count = 0
                for train_idx, val_idx in tscv.split(X):
                    fold_count += 1
                    logger.info(f"  {name} fold {fold_count}/5...")

                    X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                    y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]

                    # Skip if validation set has no positive samples
                    if y_val.sum() == 0:
                        continue

                    pipeline.fit(X_train, y_train)
                    y_pred = pipeline.predict(X_val)
                    score = trading_score(y_val, y_pred)
                    cv_scores.append(score)

                avg_score = np.mean(cv_scores) if cv_scores else 0
                training_time = (datetime.now() - start_time).total_seconds()
                logger.info(f"{name} CV score: {avg_score:.3f} (completed in {training_time:.1f}s)")

                if avg_score > best_score:
                    best_score = avg_score
                    best_model = pipeline
                    best_model_name = name

            except Exception as e:
                logger.warning(f"Failed to train {name}: {e}")
                continue
        
        # Train final model on all data
        logger.info(f"Training final {best_model_name} model on full dataset...")
        best_model.fit(X, y)
        
        # Get feature importances
        if hasattr(best_model.named_steps['classifier'], 'feature_importances_'):
            importances = best_model.named_steps['classifier'].feature_importances_
            feature_importance = pd.DataFrame({
                'feature': X.columns,
                'importance': importances
            }).sort_values('importance', ascending=False)
            
            logger.info("Top 10 features:")
            for idx, row in feature_importance.head(10).iterrows():
                logger.info(f"  {row['feature']}: {row['importance']:.3f}")
        
        # Final evaluation on last 20% of data
        split_idx = int(len(X) * 0.8)
        X_final_test = X.iloc[split_idx:]
        y_final_test = y.iloc[split_idx:]
        
        y_pred = best_model.predict(X_final_test)
        
        # Calculate performance metrics for multi-class
        performance = {
            'model_type': best_model_name,
            'cv_score': float(best_score),
            'precision': float(precision_score(y_final_test, y_pred, average='weighted', zero_division=0)),
            'recall': float(recall_score(y_final_test, y_pred, average='weighted', zero_division=0)),
            'f1_score': float(f1_score(y_final_test, y_pred, average='weighted', zero_division=0)),
            'accuracy': float(accuracy_score(y_final_test, y_pred)),
            'total_trades_predicted': int(np.sum(y_pred != 0)),  # Count non-WAIT predictions
            'total_trades_actual': int(np.sum(y_final_test != 0)),  # Count actual trades
            'buy_signals_predicted': int(np.sum(y_pred == 1)),
            'sell_signals_predicted': int(np.sum(y_pred == -1)),
            'wait_signals_predicted': int(np.sum(y_pred == 0)),
            # GPU and training information
            'gpu_info': self.gpu_info,
            'training_device': self.device,
            'training_samples': int(len(X)),
            'training_features': int(len(X.columns)),
            'training_period_days': int(training_metadata.get('training_days', 0)) if training_metadata else 0,
            'training_period_years': float(training_metadata.get('training_years', 0)) if training_metadata else 0,
            'training_start_year': int(training_metadata.get('start_year', 2021)) if training_metadata else 2021
        }
        
        # Add per-class metrics
        from sklearn.metrics import classification_report
        report = classification_report(y_final_test, y_pred, output_dict=True)
        performance['per_class_metrics'] = report
        
        return best_model, performance
    
    def _meets_deployment_criteria(self, performance: Dict) -> bool:
        """Check if model meets deployment criteria"""
        # More realistic criteria for trading
        min_precision = 0.55  # At least 55% of predicted trades should be correct
        min_cv_score = 0.45   # Decent cross-validation score
        min_f1 = 0.50        # Minimum F1 score
        
        # Log the performance metrics for debugging
        logger.info(f"Model performance metrics:")
        logger.info(f"  Precision: {performance.get('precision', 0):.3f}")
        logger.info(f"  CV Score: {performance.get('cv_score', 0):.3f}")
        logger.info(f"  F1 Score: {performance.get('f1_score', 0):.3f}")
        logger.info(f"  Trades predicted: {performance.get('total_trades_predicted', 0)}")
        
        return (
            performance.get('precision', 0) >= min_precision and
            performance.get('cv_score', 0) >= min_cv_score and
            performance.get('f1_score', 0) >= min_f1 and
            performance.get('total_trades_predicted', 0) > 10  # Must predict some trades
        )
    
    async def _deploy_model(
        self,
        model: Any,
        symbol: str,
        performance: Dict,
        feature_names: List[str]
    ) -> Dict[str, Any]:
        """Deploy model for production use"""
        
        # Create comprehensive model package
        model_package = {
            'pipeline': model,
            'feature_engineer': self.feature_engineer,
            'feature_names': feature_names,
            'symbol': symbol,
            'training_date': datetime.now().isoformat(),
            'performance': performance,
            'model_type': performance['model_type'],
            'deployment_criteria': {
                'min_precision': 0.6,
                'min_cv_score': 0.5
            }
        }
        
        # Save complete package
        package_path = Path("models") / f"{symbol}_ml_package_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        package_path.parent.mkdir(exist_ok=True)
        
        with open(package_path, 'wb') as f:
            pickle.dump(model_package, f)
        
        # Save via model management service
        model_id = await self.model_management.save_model(
            model=model_package,
            model_type=f"pattern_trader_{symbol}",
            version="4.0.0",
            training_metrics=performance,
            feature_names=feature_names,
            hyperparameters={
                'model_type': performance['model_type'],
                'training_approach': 'pattern_learning',
                'label_strategy': 'profitable_trades'
            },
            training_data_info={
                'symbol': symbol,
                'features_count': int(len(feature_names)),
                'samples_used': int(performance.get('total_trades_actual', 0))
            }
        )
        
        # Deploy
        await self.model_management.deploy_model(model_id)
        
        logger.info(f"Model deployed successfully: {model_id}")
        logger.info(f"Package saved to: {package_path}")
        
        return {'model_id': model_id, 'package_path': str(package_path)}


async def main():
    """Main training pipeline"""
    # Clear problematic environment variable BEFORE creating any objects
    import os
    if 'TRADING_SYMBOLS' in os.environ:
        logger.info(f"Clearing TRADING_SYMBOLS env var: {os.environ['TRADING_SYMBOLS']}")
        del os.environ['TRADING_SYMBOLS']

    # Check for required environment variables
    required_env_vars = ['MT5_FILES_DIR']
    missing_vars = []

    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        logger.error("Missing required environment variables:")
        for var in missing_vars:
            logger.error(f"  {var}")
        logger.error("\nPlease set these in your .env file:")
        logger.error("MT5_FILES_DIR=C:/Users/<USER>/AppData/Roaming/MetaQuotes/Terminal/YourTerminalID/MQL5/Files")
        logger.error("\nOr run this script in standalone mode with minimal configuration...")

        # Try to continue with minimal configuration
        logger.info("Attempting to continue with minimal configuration...")
        os.environ['MT5_FILES_DIR'] = 'C:/temp/mt5_files'  # Temporary fallback

    try:
        learner = TradingPatternLearner()
    except Exception as e:
        logger.error(f"Failed to initialize TradingPatternLearner: {e}")
        logger.info("This might be due to missing MT5 configuration.")
        logger.info("The GPU detection worked successfully - you have an RTX 3090 Ti!")
        return
    
    # Initialize MT5
    if not learner.mt5_client.initialize():
        logger.error("Failed to initialize MT5")
        return
    
    try:
        # Configuration - hardcode symbols to bypass settings issue
        symbols = ['EURUSD', 'GBPUSD', 'USDCAD', 'AUDUSD', 'XAUUSD', 'USOIL.cash']
        logger.info(f"Using hardcoded symbols: {symbols}")
        
        end_date = datetime.now()

        # Get start year from environment or default to 2021
        start_year = int(os.getenv('ML_TRAINING_START_YEAR', '2021'))
        start_date = datetime(start_year, 1, 1)  # Use all available data from start year onwards

        # Calculate actual training period
        training_days = (end_date - start_date).days
        training_years = training_days / 365.25

        logger.info(f"Using training data from {start_year} onwards ({training_years:.1f} years)")
        
        logger.info("="*80)
        logger.info("PRODUCTION ML TRAINING SYSTEM WITH GPU SUPPORT")
        logger.info("="*80)
        logger.info(f"Training period: {start_date.date()} to {end_date.date()} ({training_years:.1f} years)")
        logger.info(f"Training days: {training_days:,} days")
        logger.info(f"Symbols: {symbols}")
        logger.info(f"GPU Device: {learner.gpu_info['device_name']}")
        logger.info(f"CUDA Available: {learner.gpu_info['cuda_available']}")
        logger.info(f"LightGBM GPU: {learner.gpu_info['lightgbm_gpu']}")
        logger.info(f"PyTorch GPU: {learner.gpu_info['torch_gpu']}")
        logger.info("="*80)
        
        # Prepare training metadata
        training_metadata = {
            'training_days': training_days,
            'training_years': training_years,
            'start_year': start_year
        }

        results = {}

        for symbol in symbols:
            logger.info(f"\n{'='*60}")
            logger.info(f"Processing {symbol}")
            logger.info(f"{'='*60}")

            result = await learner.learn_from_patterns(symbol, start_date, end_date, training_metadata)
            results[symbol] = result
            
            if result['status'] == 'success':
                perf = result['performance']
                logger.info(f"✓ SUCCESS - Model deployed for {symbol}")
                logger.info(f"  Model Type: {perf['model_type']}")
                logger.info(f"  Training Device: {perf['training_device']}")
                logger.info(f"  Precision: {perf['precision']:.3f}")
                logger.info(f"  CV Score: {perf['cv_score']:.3f}")
                logger.info(f"  F1 Score: {perf['f1_score']:.3f}")
                logger.info(f"  Training Samples: {perf['training_samples']:,}")
                logger.info(f"  Features Used: {perf['training_features']}")
                logger.info(f"  Trades found: {perf['total_trades_actual']}")
            else:
                logger.warning(f"✗ FAILED - {symbol}: {result.get('reason', 'Unknown error')}")
        
        # Summary
        print("\n" + "="*80)
        print("TRAINING SUMMARY")
        print("="*80)
        
        successful = sum(1 for r in results.values() if r['status'] == 'success')
        print(f"\nSuccessfully deployed: {successful}/{len(symbols)} models")
        
        for symbol, result in results.items():
            print(f"\n{symbol}:")
            print(f"  Status: {result['status']}")
            if result['status'] == 'success':
                perf = result['performance']
                print(f"  Model Type: {perf['model_type']}")
                print(f"  Precision: {perf['precision']:.3f}")
                print(f"  Recall: {perf['recall']:.3f}")
                print(f"  F1 Score: {perf['f1_score']:.3f}")
                print(f"  CV Score: {perf['cv_score']:.3f}")
                print(f"  Model ID: {result['model_id']}")
        
        print("\n" + "="*80)
        print("ML models are now ready for use in trading!")
        print("To enable: Set ML_ENABLED=true in your .env file")
        print("="*80)
        
    finally:
        learner.mt5_client.shutdown()


if __name__ == "__main__":
    # Check for required dependencies
    missing_deps = []

    try:
        import lightgbm
    except ImportError:
        missing_deps.append("lightgbm")

    if not TORCH_AVAILABLE:
        missing_deps.append("torch")

    if missing_deps:
        print("Missing dependencies for optimal GPU training:")
        for dep in missing_deps:
            if dep == "lightgbm":
                print("  pip install lightgbm")
            elif dep == "torch":
                print("  pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        print("\nContinuing with available dependencies...")

    asyncio.run(main())