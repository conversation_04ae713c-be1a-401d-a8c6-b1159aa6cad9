#!/usr/bin/env python3
"""
Test GPU Setup for ML Training
Tests GPU capabilities without requiring full MT5 configuration
"""

import logging
import sys
import os
from pathlib import Path
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# GPU/CUDA support imports
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, classification_report


def detect_gpu_capabilities():
    """Detect available GPU capabilities for ML training"""
    gpu_info = {
        'cuda_available': False,
        'device': 'cpu',
        'device_name': 'CPU',
        'lightgbm_gpu': False,
        'torch_gpu': False
    }
    
    # Check CUDA availability
    if TORCH_AVAILABLE and torch.cuda.is_available():
        gpu_info['cuda_available'] = True
        gpu_info['device'] = 'cuda'
        gpu_info['device_name'] = torch.cuda.get_device_name()
        gpu_info['torch_gpu'] = True
        logger.info(f"✓ CUDA detected: {gpu_info['device_name']}")
        logger.info(f"  CUDA version: {torch.version.cuda}")
        logger.info(f"  GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # Check LightGBM GPU support
    if LIGHTGBM_AVAILABLE:
        try:
            # Test if LightGBM can use GPU
            test_lgb = lgb.LGBMClassifier(device='gpu', gpu_platform_id=0, gpu_device_id=0, verbosity=-1)
            gpu_info['lightgbm_gpu'] = True
            logger.info("✓ LightGBM GPU support available")
        except Exception as e:
            logger.info(f"LightGBM GPU not available: {e}")
    
    if not gpu_info['cuda_available']:
        logger.info("Using CPU for ML training")
    
    return gpu_info


def create_sample_data(n_samples=10000, n_features=50):
    """Create sample trading-like data for testing"""
    logger.info(f"Creating sample dataset: {n_samples} samples, {n_features} features")
    
    # Generate synthetic trading features
    np.random.seed(42)
    X = np.random.randn(n_samples, n_features)
    
    # Add some realistic trading patterns
    # Price momentum features
    X[:, 0] = np.cumsum(np.random.randn(n_samples) * 0.01)  # Price-like series
    X[:, 1] = np.diff(np.concatenate([[0], X[:, 0]]))  # Returns
    X[:, 2] = np.convolve(X[:, 1], np.ones(5)/5, mode='same')  # Moving average
    
    # Technical indicators
    for i in range(3, min(10, n_features)):
        X[:, i] = np.random.randn(n_samples) * (i * 0.1)  # Various indicators
    
    # Create labels (BUY=1, SELL=-1, WAIT=0)
    # Make it somewhat realistic - mostly WAIT signals
    y = np.zeros(n_samples)
    buy_signals = np.random.choice(n_samples, size=int(n_samples * 0.15), replace=False)
    sell_signals = np.random.choice(n_samples, size=int(n_samples * 0.15), replace=False)
    y[buy_signals] = 1
    y[sell_signals] = -1
    
    # Convert to pandas for compatibility
    feature_names = [f'feature_{i}' for i in range(n_features)]
    X_df = pd.DataFrame(X, columns=feature_names)
    y_series = pd.Series(y, name='signal')
    
    logger.info(f"Label distribution: {pd.Series(y).value_counts().to_dict()}")
    return X_df, y_series


def test_lightgbm_gpu(X, y, gpu_info):
    """Test LightGBM with GPU support"""
    if not LIGHTGBM_AVAILABLE:
        logger.warning("LightGBM not available")
        return None
    
    logger.info("Testing LightGBM...")
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Convert labels to positive integers for LightGBM
    y_train_lgb = y_train + 1  # -1,0,1 -> 0,1,2
    y_test_lgb = y_test + 1
    
    # Configure LightGBM
    lgb_params = {
        'n_estimators': 100,
        'learning_rate': 0.1,
        'num_leaves': 31,
        'random_state': 42,
        'verbosity': -1,
        'objective': 'multiclass',
        'num_class': 3
    }
    
    if gpu_info['lightgbm_gpu']:
        lgb_params.update({
            'device': 'gpu',
            'gpu_platform_id': 0,
            'gpu_device_id': 0
        })
        logger.info("Using LightGBM with GPU")
    else:
        lgb_params['n_jobs'] = -1
        logger.info("Using LightGBM with CPU")
    
    # Train and test
    start_time = datetime.now()
    model = lgb.LGBMClassifier(**lgb_params)
    model.fit(X_train, y_train_lgb)
    y_pred = model.predict(X_test)
    training_time = (datetime.now() - start_time).total_seconds()
    
    accuracy = accuracy_score(y_test_lgb, y_pred)
    logger.info(f"LightGBM - Accuracy: {accuracy:.3f}, Training time: {training_time:.1f}s")
    
    return {'model': 'LightGBM', 'accuracy': accuracy, 'time': training_time, 'gpu': gpu_info['lightgbm_gpu']}


def test_pytorch_gpu(X, y, gpu_info):
    """Test PyTorch neural network with GPU support"""
    if not TORCH_AVAILABLE:
        logger.warning("PyTorch not available")
        return None
    
    logger.info("Testing PyTorch Neural Network...")
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Convert labels to positive integers for PyTorch
    y_train_torch = y_train + 1  # -1,0,1 -> 0,1,2
    y_test_torch = y_test + 1

    # Validate label ranges
    assert y_train_torch.min() >= 0, f"Invalid train labels: min={y_train_torch.min()}"
    assert y_train_torch.max() <= 2, f"Invalid train labels: max={y_train_torch.max()}"
    assert y_test_torch.min() >= 0, f"Invalid test labels: min={y_test_torch.min()}"
    assert y_test_torch.max() <= 2, f"Invalid test labels: max={y_test_torch.max()}"

    logger.info(f"PyTorch label distribution - Train: {y_train_torch.value_counts().to_dict()}")
    logger.info(f"PyTorch label distribution - Test: {y_test_torch.value_counts().to_dict()}")

    device = gpu_info['device']

    # Simple neural network
    class SimpleNN(nn.Module):
        def __init__(self, input_size):
            super(SimpleNN, self).__init__()
            self.network = nn.Sequential(
                nn.Linear(input_size, 128),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(128, 64),
                nn.ReLU(),
                nn.Dropout(0.3),
                nn.Linear(64, 3)  # 3 classes
            )

        def forward(self, x):
            return self.network(x)

    # Prepare data
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    X_train_tensor = torch.FloatTensor(X_train_scaled).to(device)
    y_train_tensor = torch.LongTensor(y_train_torch.values).to(device)
    X_test_tensor = torch.FloatTensor(X_test_scaled).to(device)
    y_test_tensor = torch.LongTensor(y_test_torch.values).to(device)
    
    # Train model
    model = SimpleNN(X_train.shape[1]).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()
    
    start_time = datetime.now()
    
    # Training loop
    model.train()
    for epoch in range(50):  # Quick test
        optimizer.zero_grad()
        outputs = model(X_train_tensor)
        loss = criterion(outputs, y_train_tensor)
        loss.backward()
        optimizer.step()
        
        if epoch % 10 == 0:
            logger.info(f"Epoch {epoch}, Loss: {loss.item():.4f}")
    
    # Test
    model.eval()
    with torch.no_grad():
        test_outputs = model(X_test_tensor)
        _, predicted = torch.max(test_outputs, 1)
        accuracy = (predicted == y_test_tensor).float().mean().item()
    
    training_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"PyTorch NN - Accuracy: {accuracy:.3f}, Training time: {training_time:.1f}s")
    
    return {'model': 'PyTorch NN', 'accuracy': accuracy, 'time': training_time, 'gpu': gpu_info['torch_gpu']}


def main():
    """Main test function"""
    logger.info("="*80)
    logger.info("GPU SETUP TEST FOR GPT TRADING SYSTEM")
    logger.info("="*80)
    
    # Detect GPU capabilities
    gpu_info = detect_gpu_capabilities()
    
    logger.info(f"\nGPU Information:")
    logger.info(f"  Device: {gpu_info['device_name']}")
    logger.info(f"  CUDA Available: {gpu_info['cuda_available']}")
    logger.info(f"  LightGBM GPU: {gpu_info['lightgbm_gpu']}")
    logger.info(f"  PyTorch GPU: {gpu_info['torch_gpu']}")
    
    # Create sample data
    logger.info("\n" + "="*60)
    logger.info("CREATING SAMPLE DATA")
    logger.info("="*60)
    X, y = create_sample_data(n_samples=5000, n_features=30)  # Smaller for quick test
    
    # Test models
    results = []
    
    logger.info("\n" + "="*60)
    logger.info("TESTING ML MODELS")
    logger.info("="*60)
    
    # Test LightGBM
    lgb_result = test_lightgbm_gpu(X, y, gpu_info)
    if lgb_result:
        results.append(lgb_result)
    
    # Test PyTorch
    torch_result = test_pytorch_gpu(X, y, gpu_info)
    if torch_result:
        results.append(torch_result)
    
    # Summary
    logger.info("\n" + "="*80)
    logger.info("TEST RESULTS SUMMARY")
    logger.info("="*80)
    
    for result in results:
        gpu_status = "GPU" if result['gpu'] else "CPU"
        logger.info(f"{result['model']:15} | {gpu_status:3} | Accuracy: {result['accuracy']:.3f} | Time: {result['time']:.1f}s")
    
    if gpu_info['cuda_available']:
        logger.info(f"\n✅ GPU SETUP SUCCESSFUL!")
        logger.info(f"Your {gpu_info['device_name']} is ready for ML training!")
    else:
        logger.info(f"\n⚠️  No GPU detected - will use CPU training")
    
    logger.info("\nNext steps:")
    logger.info("1. Ensure your .env file has MT5_FILES_DIR configured")
    logger.info("2. Run: python scripts/train_ml_production.py")
    logger.info("="*80)


if __name__ == "__main__":
    main()
