#!/usr/bin/env python3
"""
Test XGBoost and CatBoost label conversion
"""

import numpy as np
import pandas as pd
import logging
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test imports
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False

def create_test_data():
    """Create test data with trading labels {-1, 0, 1}"""
    np.random.seed(42)
    
    # Create features
    n_samples = 1000
    n_features = 10
    X = np.random.randn(n_samples, n_features)
    
    # Create trading labels: -1 (SELL), 0 (WAIT), 1 (BUY)
    y = np.random.choice([-1, 0, 1], size=n_samples, p=[0.3, 0.4, 0.3])
    
    X_df = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(n_features)])
    y_series = pd.Series(y)
    
    logger.info(f"Created test data: {n_samples} samples, {n_features} features")
    logger.info(f"Label distribution: {y_series.value_counts().to_dict()}")
    
    return X_df, y_series

def test_xgboost_wrapper():
    """Test XGBoost with label conversion"""
    if not XGBOOST_AVAILABLE:
        logger.warning("XGBoost not available, skipping test")
        return False
    
    logger.info("Testing XGBoost wrapper...")
    
    # XGBoost wrapper that handles label conversion
    class XGBWrapper:
        def __init__(self, **kwargs):
            self.model = xgb.XGBClassifier(**kwargs)
            self.label_encoder = None
        
        def fit(self, X, y):
            # Convert labels from {-1, 0, 1} to {0, 1, 2}
            y_encoded = y + 1
            self.label_encoder = {-1: 0, 0: 1, 1: 2}  # Store mapping
            logger.info(f"XGB: Converting labels {y.unique()} to {y_encoded.unique()}")
            self.model.fit(X, y_encoded)
            return self
        
        def predict(self, X):
            # Get predictions and convert back to original labels
            y_pred_encoded = self.model.predict(X)
            y_pred = y_pred_encoded - 1  # Convert {0, 1, 2} back to {-1, 0, 1}
            logger.info(f"XGB: Converting predictions {np.unique(y_pred_encoded)} to {np.unique(y_pred)}")
            return y_pred
        
        def predict_proba(self, X):
            return self.model.predict_proba(X)
    
    # Create test data
    X, y = create_test_data()
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    try:
        # Test the wrapper
        model = XGBWrapper(
            n_estimators=50,
            random_state=42,
            n_jobs=1,  # Single thread for testing
            eval_metric='mlogloss'
        )
        
        # Fit and predict
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        
        # Check predictions are in correct range
        unique_preds = np.unique(y_pred)
        expected_labels = [-1, 0, 1]
        
        logger.info(f"XGB predictions range: {unique_preds}")
        logger.info(f"Expected range: {expected_labels}")
        
        if all(pred in expected_labels for pred in unique_preds):
            accuracy = accuracy_score(y_test, y_pred)
            logger.info(f"✓ XGBoost wrapper working! Accuracy: {accuracy:.3f}")
            return True
        else:
            logger.error(f"✗ XGBoost predictions out of range: {unique_preds}")
            return False
            
    except Exception as e:
        logger.error(f"✗ XGBoost wrapper failed: {e}")
        return False

def test_catboost_wrapper():
    """Test CatBoost with label conversion"""
    if not CATBOOST_AVAILABLE:
        logger.warning("CatBoost not available, skipping test")
        return False
    
    logger.info("Testing CatBoost wrapper...")
    
    # CatBoost wrapper that handles label conversion
    class CatBoostWrapper:
        def __init__(self, **kwargs):
            self.model = cb.CatBoostClassifier(**kwargs)
            self.label_encoder = None
        
        def fit(self, X, y):
            # Convert labels from {-1, 0, 1} to {0, 1, 2}
            y_encoded = y + 1
            self.label_encoder = {-1: 0, 0: 1, 1: 2}  # Store mapping
            logger.info(f"CatBoost: Converting labels {y.unique()} to {y_encoded.unique()}")
            self.model.fit(X, y_encoded)
            return self
        
        def predict(self, X):
            # Get predictions and convert back to original labels
            y_pred_encoded = self.model.predict(X)
            y_pred = y_pred_encoded - 1  # Convert {0, 1, 2} back to {-1, 0, 1}
            logger.info(f"CatBoost: Converting predictions {np.unique(y_pred_encoded)} to {np.unique(y_pred)}")
            return y_pred
        
        def predict_proba(self, X):
            return self.model.predict_proba(X)
    
    # Create test data
    X, y = create_test_data()
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    try:
        # Test the wrapper
        model = CatBoostWrapper(
            iterations=50,
            random_seed=42,
            thread_count=1,  # Single thread for testing
            verbose=False
        )
        
        # Fit and predict
        model.fit(X_train, y_train)
        y_pred = model.predict(X_test)
        
        # Check predictions are in correct range
        unique_preds = np.unique(y_pred)
        expected_labels = [-1, 0, 1]
        
        logger.info(f"CatBoost predictions range: {unique_preds}")
        logger.info(f"Expected range: {expected_labels}")
        
        if all(pred in expected_labels for pred in unique_preds):
            accuracy = accuracy_score(y_test, y_pred)
            logger.info(f"✓ CatBoost wrapper working! Accuracy: {accuracy:.3f}")
            return True
        else:
            logger.error(f"✗ CatBoost predictions out of range: {unique_preds}")
            return False
            
    except Exception as e:
        logger.error(f"✗ CatBoost wrapper failed: {e}")
        return False

def main():
    logger.info("="*60)
    logger.info("BOOSTING LABEL CONVERSION TEST")
    logger.info("="*60)
    
    success = True
    
    # Test XGBoost wrapper
    if XGBOOST_AVAILABLE:
        success &= test_xgboost_wrapper()
    else:
        logger.warning("XGBoost not available - install with: pip install xgboost")
    
    # Test CatBoost wrapper
    if CATBOOST_AVAILABLE:
        success &= test_catboost_wrapper()
    else:
        logger.warning("CatBoost not available - install with: pip install catboost")
    
    logger.info("\n" + "="*60)
    if success:
        logger.info("✅ ALL TESTS PASSED!")
        logger.info("Multi-threaded boosting with label conversion is working!")
        logger.info("You can now run: python scripts/train_ml_production.py")
    else:
        logger.error("❌ SOME TESTS FAILED!")
        logger.error("Check the logs above for details")
    logger.info("="*60)

if __name__ == "__main__":
    main()
