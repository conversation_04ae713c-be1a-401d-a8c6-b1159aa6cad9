#!/usr/bin/env python3
"""
Check if TA-Lib is actually used in the codebase
"""

import os
import re
from pathlib import Path

def search_talib_usage():
    """Search for TA-Lib usage in Python files"""
    talib_patterns = [
        r'import\s+talib',
        r'from\s+talib',
        r'talib\.',
        r'TA_',  # TA-Lib function prefix
    ]
    
    ta_patterns = [
        r'import\s+ta\b',
        r'from\s+ta\b',
        r'ta\.',
    ]
    
    project_root = Path('.')
    python_files = list(project_root.rglob('*.py'))
    
    talib_files = []
    ta_files = []
    
    print("Searching for TA-Lib and 'ta' library usage...")
    print("="*60)
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Check for TA-Lib usage
                for pattern in talib_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        talib_files.append(str(file_path))
                        break
                
                # Check for 'ta' library usage
                for pattern in ta_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        ta_files.append(str(file_path))
                        break
                        
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
    
    print(f"\nTA-Lib usage found in {len(talib_files)} files:")
    for file_path in talib_files:
        print(f"  - {file_path}")
    
    print(f"\n'ta' library usage found in {len(ta_files)} files:")
    for file_path in ta_files:
        print(f"  - {file_path}")
    
    return talib_files, ta_files

def check_technical_indicators():
    """Check what technical indicators are being used"""
    print("\n" + "="*60)
    print("TECHNICAL INDICATOR ANALYSIS")
    print("="*60)
    
    # Common technical indicators
    indicators = [
        'sma', 'ema', 'rsi', 'macd', 'bollinger', 'stochastic',
        'atr', 'adx', 'cci', 'williams', 'momentum', 'roc',
        'moving_average', 'exponential', 'relative_strength'
    ]
    
    project_root = Path('.')
    python_files = list(project_root.rglob('*.py'))
    
    indicator_usage = {}
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()
                
                for indicator in indicators:
                    if indicator in content:
                        if indicator not in indicator_usage:
                            indicator_usage[indicator] = []
                        indicator_usage[indicator].append(str(file_path))
                        
        except Exception as e:
            continue
    
    print("Technical indicators found:")
    for indicator, files in indicator_usage.items():
        print(f"\n{indicator.upper()}:")
        for file_path in files:
            print(f"  - {file_path}")

def recommend_solution():
    """Recommend the best solution"""
    print("\n" + "="*60)
    print("RECOMMENDATIONS")
    print("="*60)
    
    talib_files, ta_files = search_talib_usage()
    
    if not talib_files and ta_files:
        print("✅ GOOD NEWS: You're already using the 'ta' library!")
        print("   No need to install TA-Lib - 'ta' is pure Python and easier.")
        print("\n   Current setup is optimal.")
        
    elif talib_files and not ta_files:
        print("⚠️  You're using TA-Lib. Options:")
        print("   1. Install TA-Lib with pre-compiled wheel (recommended):")
        print("      pip install --find-links https://github.com/cgohlke/talib-build/releases/latest/download/ TA-Lib")
        print("\n   2. Replace TA-Lib with 'ta' library (easier maintenance):")
        print("      - 'ta' provides the same indicators")
        print("      - Pure Python, no compilation needed")
        print("      - Already in your requirements.txt")
        
    elif talib_files and ta_files:
        print("⚠️  You're using BOTH TA-Lib and 'ta' libraries.")
        print("   Recommendation: Standardize on 'ta' library for easier maintenance.")
        
    else:
        print("ℹ️  No technical analysis libraries detected.")
        print("   The 'ta' library is included in requirements.txt if needed.")

def main():
    print("TA-LIB USAGE CHECKER")
    print("="*60)
    
    search_talib_usage()
    check_technical_indicators()
    recommend_solution()

if __name__ == "__main__":
    main()
