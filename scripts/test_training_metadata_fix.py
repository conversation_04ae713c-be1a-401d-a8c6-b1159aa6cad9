#!/usr/bin/env python3
"""
Test the training_metadata scope fix
"""

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_method_signature():
    """Test that the method signature is correct"""
    
    # Read the training script
    with open('scripts/train_ml_production.py', 'r') as f:
        content = f.read()
    
    # Check that the method signature includes training_metadata
    if 'def _train_advanced_models(' in content:
        # Find the method definition
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'def _train_advanced_models(' in line:
                # Get the method signature (next few lines)
                method_lines = []
                j = i
                while j < len(lines) and not lines[j].strip().endswith('):'):
                    method_lines.append(lines[j])
                    j += 1
                if j < len(lines):
                    method_lines.append(lines[j])  # Add the closing line
                
                method_signature = '\n'.join(method_lines)
                logger.info("Method signature found:")
                logger.info(method_signature)
                
                if 'training_metadata' in method_signature:
                    logger.info("✅ training_metadata parameter found in method signature")
                    return True
                else:
                    logger.error("❌ training_metadata parameter NOT found in method signature")
                    return False
    
    logger.error("❌ _train_advanced_models method not found")
    return False

def test_method_call():
    """Test that the method call passes training_metadata"""
    
    with open('scripts/train_ml_production.py', 'r') as f:
        content = f.read()
    
    # Check that the method call includes training_metadata
    if 'await self._train_advanced_models(' in content:
        lines = content.split('\n')
        for line in lines:
            if 'await self._train_advanced_models(' in line:
                logger.info(f"Method call found: {line.strip()}")
                
                if 'training_metadata' in line:
                    logger.info("✅ training_metadata parameter passed in method call")
                    return True
                else:
                    logger.error("❌ training_metadata parameter NOT passed in method call")
                    return False
    
    logger.error("❌ Method call not found")
    return False

def test_performance_dict():
    """Test that performance dict uses training_metadata correctly"""
    
    with open('scripts/train_ml_production.py', 'r') as f:
        content = f.read()
    
    # Check for the performance dictionary construction
    if 'training_period_days' in content and 'training_metadata.get' in content:
        logger.info("✅ Performance dict uses training_metadata.get() pattern")
        return True
    else:
        logger.error("❌ Performance dict doesn't use training_metadata properly")
        return False

def main():
    logger.info("="*60)
    logger.info("TRAINING METADATA SCOPE FIX TEST")
    logger.info("="*60)
    
    success = True
    
    # Test method signature
    logger.info("\n1. Testing method signature...")
    success &= test_method_signature()
    
    # Test method call
    logger.info("\n2. Testing method call...")
    success &= test_method_call()
    
    # Test performance dict
    logger.info("\n3. Testing performance dict...")
    success &= test_performance_dict()
    
    logger.info("\n" + "="*60)
    if success:
        logger.info("✅ ALL TESTS PASSED!")
        logger.info("The training_metadata scope issue has been fixed")
        logger.info("You can now run: python scripts/train_ml_production.py")
    else:
        logger.error("❌ SOME TESTS FAILED!")
        logger.error("The training_metadata scope issue needs more work")
    logger.info("="*60)

if __name__ == "__main__":
    main()
