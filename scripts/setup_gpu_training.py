#!/usr/bin/env python3
"""
GPU Training Setup Script for GPT Trading System
Detects GPU capabilities and installs appropriate dependencies
"""

import subprocess
import sys
import platform
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_cuda_availability():
    """Check if CUDA is available on the system"""
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✓ NVIDIA GPU detected")
            return True
        else:
            logger.info("No NVIDIA GPU detected")
            return False
    except FileNotFoundError:
        logger.info("nvidia-smi not found - no NVIDIA GPU or drivers")
        return False


def get_cuda_version():
    """Get CUDA version if available"""
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            output = result.stdout
            for line in output.split('\n'):
                if 'release' in line.lower():
                    version = line.split('release')[1].split(',')[0].strip()
                    logger.info(f"CUDA version: {version}")
                    return version
    except FileNotFoundError:
        logger.info("nvcc not found - CUDA toolkit not installed")
    return None


def install_pytorch_gpu():
    """Install PyTorch with GPU support"""
    cuda_available = check_cuda_availability()
    cuda_version = get_cuda_version()
    
    if cuda_available:
        if cuda_version and '12.1' in cuda_version:
            cmd = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio', 
                   '--index-url', 'https://download.pytorch.org/whl/cu121']
            logger.info("Installing PyTorch for CUDA 12.1...")
        elif cuda_version and '11.8' in cuda_version:
            cmd = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio', 
                   '--index-url', 'https://download.pytorch.org/whl/cu118']
            logger.info("Installing PyTorch for CUDA 11.8...")
        else:
            # Default to CUDA 11.8 (most compatible)
            cmd = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio', 
                   '--index-url', 'https://download.pytorch.org/whl/cu118']
            logger.info("Installing PyTorch for CUDA 11.8 (default)...")
    else:
        cmd = [sys.executable, '-m', 'pip', 'install', 'torch', 'torchvision', 'torchaudio', 
               '--index-url', 'https://download.pytorch.org/whl/cpu']
        logger.info("Installing PyTorch for CPU only...")
    
    try:
        subprocess.run(cmd, check=True)
        logger.info("✓ PyTorch installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install PyTorch: {e}")
        return False


def install_lightgbm_gpu():
    """Install LightGBM with GPU support if possible"""
    cuda_available = check_cuda_availability()
    
    if cuda_available:
        logger.info("Attempting to install LightGBM with GPU support...")
        try:
            # Try to install with GPU support
            cmd = [sys.executable, '-m', 'pip', 'install', 'lightgbm', 
                   '--config-settings=cmake.define.USE_GPU=ON']
            subprocess.run(cmd, check=True)
            logger.info("✓ LightGBM with GPU support installed")
            return True
        except subprocess.CalledProcessError:
            logger.warning("Failed to install LightGBM with GPU support, falling back to CPU version")
    
    # Fallback to CPU version
    try:
        cmd = [sys.executable, '-m', 'pip', 'install', 'lightgbm']
        subprocess.run(cmd, check=True)
        logger.info("✓ LightGBM (CPU version) installed")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install LightGBM: {e}")
        return False


def install_additional_deps():
    """Install additional ML dependencies from requirements.txt"""
    try:
        # Install from main requirements file (now includes GPU support)
        cmd = [sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt']
        subprocess.run(cmd, check=True)
        logger.info("✓ All dependencies installed from requirements.txt")
        return True
    except subprocess.CalledProcessError as e:
        logger.warning(f"Failed to install from requirements.txt: {e}")

        # Fallback to individual packages
        deps = [
            'scikit-learn>=1.3.0',
            'xgboost',      # Multi-threaded gradient boosting
            'catboost',     # Multi-threaded gradient boosting
            'psutil',
            'GPUtil',
            'hf_xet'        # Hugging Face optimization
        ]

        for dep in deps:
            try:
                cmd = [sys.executable, '-m', 'pip', 'install', dep]
                subprocess.run(cmd, check=True)
                logger.info(f"✓ {dep} installed")
            except subprocess.CalledProcessError as e:
                logger.warning(f"Failed to install {dep}: {e}")
        return False


def test_gpu_setup():
    """Test if GPU setup is working"""
    logger.info("Testing GPU setup...")
    
    # Test PyTorch
    try:
        import torch
        logger.info(f"✓ PyTorch {torch.__version__} available")
        if torch.cuda.is_available():
            logger.info(f"✓ CUDA available: {torch.cuda.get_device_name()}")
        else:
            logger.info("PyTorch using CPU")
    except ImportError:
        logger.error("✗ PyTorch not available")
    
    # Test LightGBM
    try:
        import lightgbm as lgb
        logger.info(f"✓ LightGBM {lgb.__version__} available")

        # Test GPU support
        try:
            test_lgb = lgb.LGBMClassifier(device='gpu', verbosity=-1)
            logger.info("✓ LightGBM GPU support available")
        except Exception:
            logger.info("LightGBM using CPU")
    except ImportError:
        logger.error("✗ LightGBM not available")

    # Test XGBoost
    try:
        import xgboost as xgb
        logger.info(f"✓ XGBoost {xgb.__version__} available (multi-threaded)")
    except ImportError:
        logger.error("✗ XGBoost not available")

    # Test CatBoost
    try:
        import catboost as cb
        logger.info(f"✓ CatBoost {cb.__version__} available (multi-threaded)")
    except ImportError:
        logger.error("✗ CatBoost not available")


def main():
    """Main setup function"""
    logger.info("="*60)
    logger.info("GPU Training Setup for GPT Trading System")
    logger.info("="*60)
    
    # Check system
    logger.info(f"Platform: {platform.system()} {platform.release()}")
    logger.info(f"Python: {sys.version}")
    
    # Check GPU
    cuda_available = check_cuda_availability()
    get_cuda_version()
    
    # Install dependencies
    logger.info("\nInstalling dependencies...")
    
    success = True
    success &= install_pytorch_gpu()
    success &= install_lightgbm_gpu()
    install_additional_deps()
    
    # Test setup
    logger.info("\nTesting installation...")
    test_gpu_setup()
    
    if success:
        logger.info("\n" + "="*60)
        logger.info("✓ GPU training setup completed successfully!")
        logger.info("You can now run: python scripts/train_ml_production.py")
        logger.info("="*60)
    else:
        logger.warning("\n" + "="*60)
        logger.warning("⚠ Setup completed with some issues")
        logger.warning("Check the logs above for details")
        logger.warning("="*60)


if __name__ == "__main__":
    main()
