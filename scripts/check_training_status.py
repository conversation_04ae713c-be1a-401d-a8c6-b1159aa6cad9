#!/usr/bin/env python3
"""
Check training status and provide recommendations
"""

import psutil
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def find_training_processes():
    """Find any running ML training processes"""
    training_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            if 'train_ml_production.py' in cmdline:
                training_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return training_processes

def monitor_training():
    """Monitor training process performance"""
    processes = find_training_processes()
    
    if not processes:
        logger.info("No ML training processes found")
        return
    
    for proc in processes:
        logger.info(f"Found training process: PID {proc.pid}")
        logger.info(f"CPU usage: {proc.cpu_percent():.1f}%")
        logger.info(f"Memory usage: {proc.memory_percent():.1f}%")
        
        # Check if process is stuck (very low CPU for extended time)
        cpu_samples = []
        for i in range(5):
            cpu_samples.append(proc.cpu_percent(interval=1))
            logger.info(f"CPU sample {i+1}: {cpu_samples[-1]:.1f}%")
        
        avg_cpu = sum(cpu_samples) / len(cpu_samples)
        
        if avg_cpu < 5.0:
            logger.warning(f"Process appears stuck (avg CPU: {avg_cpu:.1f}%)")
            logger.warning("Consider restarting with optimized parameters")
            
            # Get process runtime
            create_time = proc.create_time()
            runtime = time.time() - create_time
            logger.info(f"Process has been running for {runtime/60:.1f} minutes")
            
            if runtime > 600:  # 10 minutes
                logger.error("Process has been running for over 10 minutes with low CPU")
                logger.error("Recommendation: Kill and restart with faster settings")
                
                print("\nTo kill the stuck process:")
                print(f"taskkill /PID {proc.pid} /F")
                print("\nThen restart training with the optimized script")
        else:
            logger.info(f"Process is active (avg CPU: {avg_cpu:.1f}%)")

def main():
    logger.info("Checking ML training status...")
    monitor_training()
    
    print("\nOptimizations made to train_ml_production.py:")
    print("- Reduced Random Forest estimators: 400 → 200")
    print("- Reduced Random Forest depth: 12 → 10") 
    print("- Increased min_samples_split: 15 → 50")
    print("- Added max_samples=0.8 for faster training")
    print("- Reduced Gradient Boosting estimators: 300 → 150")
    print("- Added progress logging for each fold")
    print("\nThese changes should make training 2-3x faster!")

if __name__ == "__main__":
    main()
