#!/usr/bin/env python3
"""
Test that all merged requirements are working correctly
"""

import logging
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_core_ml_libraries():
    """Test core ML libraries"""
    logger.info("Testing core ML libraries...")
    
    try:
        import sklearn
        logger.info(f"✓ scikit-learn {sklearn.__version__}")
    except ImportError:
        logger.error("✗ scikit-learn not available")
        return False
    
    try:
        import pandas as pd
        logger.info(f"✓ pandas {pd.__version__}")
    except ImportError:
        logger.error("✗ pandas not available")
        return False
    
    try:
        import numpy as np
        logger.info(f"✓ numpy {np.__version__}")
    except ImportError:
        logger.error("✗ numpy not available")
        return False
    
    return True

def test_gpu_boosting_libraries():
    """Test GPU-accelerated boosting libraries"""
    logger.info("Testing GPU boosting libraries...")
    
    try:
        import xgboost as xgb
        logger.info(f"✓ XGBoost {xgb.__version__}")
    except ImportError:
        logger.error("✗ XGBoost not available")
        return False
    
    try:
        import catboost as cb
        logger.info(f"✓ CatBoost {cb.__version__}")
    except ImportError:
        logger.error("✗ CatBoost not available")
        return False
    
    try:
        import lightgbm as lgb
        logger.info(f"✓ LightGBM {lgb.__version__}")
    except ImportError:
        logger.error("✗ LightGBM not available")
        return False
    
    return True

def test_pytorch_gpu():
    """Test PyTorch with GPU support"""
    logger.info("Testing PyTorch GPU support...")
    
    try:
        import torch
        logger.info(f"✓ PyTorch {torch.__version__}")
        
        if torch.cuda.is_available():
            logger.info(f"✓ CUDA available: {torch.cuda.get_device_name()}")
        else:
            logger.info("⚠ CUDA not available (CPU only)")
        
        import torchvision
        logger.info(f"✓ torchvision {torchvision.__version__}")
        
        import torchaudio
        logger.info(f"✓ torchaudio {torchaudio.__version__}")
        
        return True
    except ImportError as e:
        logger.error(f"✗ PyTorch components not available: {e}")
        return False

def test_huggingface_optimization():
    """Test Hugging Face optimization"""
    logger.info("Testing Hugging Face optimization...")
    
    try:
        import sentence_transformers
        logger.info(f"✓ sentence-transformers {sentence_transformers.__version__}")
    except ImportError:
        logger.error("✗ sentence-transformers not available")
        return False
    
    try:
        import hf_xet
        logger.info(f"✓ hf_xet available (optimized downloads)")
    except ImportError:
        logger.warning("⚠ hf_xet not available (will use standard downloads)")
        return True  # Not critical
    
    return True

def test_gpu_monitoring():
    """Test GPU monitoring tools"""
    logger.info("Testing GPU monitoring...")
    
    try:
        import GPUtil
        logger.info(f"✓ GPUtil available")
        
        # Test GPU detection
        gpus = GPUtil.getGPUs()
        if gpus:
            for gpu in gpus:
                logger.info(f"  GPU {gpu.id}: {gpu.name} ({gpu.memoryTotal}MB)")
        else:
            logger.info("  No GPUs detected")
        
    except ImportError:
        logger.error("✗ GPUtil not available")
        return False
    
    try:
        import psutil
        logger.info(f"✓ psutil {psutil.__version__}")
    except ImportError:
        logger.error("✗ psutil not available")
        return False
    
    return True

def main():
    logger.info("="*60)
    logger.info("MERGED REQUIREMENTS TEST")
    logger.info("="*60)
    
    success = True
    
    # Test core ML libraries
    success &= test_core_ml_libraries()
    
    # Test GPU boosting libraries
    success &= test_gpu_boosting_libraries()
    
    # Test PyTorch GPU support
    success &= test_pytorch_gpu()
    
    # Test Hugging Face optimization
    success &= test_huggingface_optimization()
    
    # Test GPU monitoring
    success &= test_gpu_monitoring()
    
    logger.info("\n" + "="*60)
    if success:
        logger.info("✅ ALL REQUIREMENTS WORKING!")
        logger.info("Your system is ready for GPU-accelerated ML training")
        logger.info("\nNext steps:")
        logger.info("1. Run: python scripts/train_ml_production.py")
        logger.info("2. Run: python trading_loop.py")
    else:
        logger.error("❌ SOME REQUIREMENTS MISSING!")
        logger.error("Install missing packages with:")
        logger.error("pip install -r requirements.txt")
    logger.info("="*60)

if __name__ == "__main__":
    main()
