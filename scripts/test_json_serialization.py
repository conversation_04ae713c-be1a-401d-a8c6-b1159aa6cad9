#!/usr/bin/env python3
"""
Test JSON serialization with NumPy types
"""

import json
import numpy as np
import logging
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from core.services.model_management_service import json_serialize_safe, safe_json_dumps

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_numpy_serialization():
    """Test serialization of NumPy types"""
    logger.info("Testing NumPy type serialization...")
    
    # Create test data with NumPy types
    test_data = {
        'int64_value': np.int64(42),
        'int32_value': np.int32(123),
        'float64_value': np.float64(3.14159),
        'float32_value': np.float32(2.718),
        'array_value': np.array([1, 2, 3, 4, 5]),
        'nested_dict': {
            'numpy_int': np.int64(999),
            'numpy_float': np.float64(1.23),
            'regular_int': 456,
            'regular_float': 7.89
        },
        'regular_string': 'hello world',
        'regular_list': [1, 2, 3]
    }
    
    logger.info("Original data types:")
    for key, value in test_data.items():
        logger.info(f"  {key}: {type(value)} = {value}")
    
    # Test standard json.dumps (should fail)
    try:
        standard_json = json.dumps(test_data)
        logger.error("Standard JSON serialization should have failed!")
    except TypeError as e:
        logger.info(f"✓ Standard JSON failed as expected: {e}")
    
    # Test safe serialization
    try:
        safe_json = safe_json_dumps(test_data)
        logger.info("✓ Safe JSON serialization succeeded")
        
        # Test deserialization
        deserialized = json.loads(safe_json)
        logger.info("✓ Deserialization succeeded")
        
        logger.info("Deserialized data types:")
        for key, value in deserialized.items():
            logger.info(f"  {key}: {type(value)} = {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Safe JSON serialization failed: {e}")
        return False

def test_model_metadata_serialization():
    """Test serialization of typical model metadata"""
    logger.info("\nTesting model metadata serialization...")
    
    # Simulate typical model metadata with NumPy types
    training_metrics = {
        'accuracy': np.float64(0.85),
        'precision': np.float64(0.82),
        'recall': np.float64(0.88),
        'f1_score': np.float64(0.85),
        'total_samples': np.int64(18514),
        'training_time': np.float32(125.5)
    }
    
    hyperparameters = {
        'n_estimators': np.int64(200),
        'max_depth': np.int64(10),
        'learning_rate': np.float64(0.1),
        'random_state': np.int64(42)
    }
    
    training_data_info = {
        'features_count': np.int64(67),
        'samples_used': np.int64(18514),
        'training_period_days': np.int64(1637)
    }
    
    logger.info("Testing training_metrics serialization...")
    try:
        metrics_json = safe_json_dumps(training_metrics)
        logger.info("✓ Training metrics serialized successfully")
    except Exception as e:
        logger.error(f"✗ Training metrics serialization failed: {e}")
        return False
    
    logger.info("Testing hyperparameters serialization...")
    try:
        params_json = safe_json_dumps(hyperparameters)
        logger.info("✓ Hyperparameters serialized successfully")
    except Exception as e:
        logger.error(f"✗ Hyperparameters serialization failed: {e}")
        return False
    
    logger.info("Testing training_data_info serialization...")
    try:
        info_json = safe_json_dumps(training_data_info)
        logger.info("✓ Training data info serialized successfully")
    except Exception as e:
        logger.error(f"✗ Training data info serialization failed: {e}")
        return False
    
    return True

def main():
    logger.info("="*60)
    logger.info("JSON SERIALIZATION TEST")
    logger.info("="*60)
    
    success = True
    
    # Test basic NumPy serialization
    success &= test_numpy_serialization()
    
    # Test model metadata serialization
    success &= test_model_metadata_serialization()
    
    logger.info("\n" + "="*60)
    if success:
        logger.info("✅ ALL TESTS PASSED!")
        logger.info("JSON serialization fix is working correctly")
    else:
        logger.error("❌ SOME TESTS FAILED!")
        logger.error("JSON serialization needs more work")
    logger.info("="*60)

if __name__ == "__main__":
    main()
