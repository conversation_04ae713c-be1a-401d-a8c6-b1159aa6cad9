#!/usr/bin/env python3
"""
Install fast multi-threaded gradient boosting libraries
"""

import subprocess
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_xgboost():
    """Install XGBoost"""
    logger.info("Installing XGBoost...")
    try:
        cmd = [sys.executable, '-m', 'pip', 'install', 'xgboost']
        subprocess.run(cmd, check=True)
        logger.info("✓ XGBoost installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install XGBoost: {e}")
        return False

def install_catboost():
    """Install CatBoost"""
    logger.info("Installing CatBoost...")
    try:
        cmd = [sys.executable, '-m', 'pip', 'install', 'catboost']
        subprocess.run(cmd, check=True)
        logger.info("✓ CatBoost installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install CatBoost: {e}")
        return False

def test_installations():
    """Test that the libraries work"""
    logger.info("Testing installations...")
    
    # Test XGBoost
    try:
        import xgboost as xgb
        logger.info(f"✓ XGBoost {xgb.__version__} working")
        
        # Test multi-threading
        clf = xgb.XGBClassifier(n_jobs=-1, random_state=42)
        logger.info("✓ XGBoost multi-threading available")
    except ImportError:
        logger.error("✗ XGBoost not working")
    except Exception as e:
        logger.warning(f"XGBoost issue: {e}")
    
    # Test CatBoost
    try:
        import catboost as cb
        logger.info(f"✓ CatBoost {cb.__version__} working")
        
        # Test multi-threading
        clf = cb.CatBoostClassifier(thread_count=-1, verbose=False)
        logger.info("✓ CatBoost multi-threading available")
    except ImportError:
        logger.error("✗ CatBoost not working")
    except Exception as e:
        logger.warning(f"CatBoost issue: {e}")

def main():
    logger.info("="*60)
    logger.info("FAST MULTI-THREADED BOOSTING INSTALLATION")
    logger.info("="*60)
    
    success = True
    
    # Install XGBoost
    success &= install_xgboost()
    
    # Install CatBoost
    success &= install_catboost()
    
    # Test installations
    logger.info("\nTesting installations...")
    test_installations()
    
    logger.info("\n" + "="*60)
    if success:
        logger.info("✅ INSTALLATION COMPLETED!")
        logger.info("Your gradient boosting training will now be much faster!")
        logger.info("\nBenefits:")
        logger.info("- XGBoost: 3-5x faster than scikit-learn GB")
        logger.info("- CatBoost: 2-4x faster than scikit-learn GB")
        logger.info("- Both use all CPU cores automatically")
        logger.info("\nRun: python scripts/train_ml_production.py")
    else:
        logger.warning("⚠️ INSTALLATION HAD ISSUES")
        logger.warning("Check the logs above for details")
    logger.info("="*60)

if __name__ == "__main__":
    main()
