#!/usr/bin/env python3
"""
Test label conversion for PyTorch training
Verifies that label conversion from {-1, 0, 1} to {0, 1, 2} works correctly
"""

import numpy as np
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_label_conversion():
    """Test label conversion logic"""
    logger.info("Testing label conversion for PyTorch...")
    
    # Create sample labels with trading signals
    # -1 = SELL, 0 = WAIT, 1 = BUY
    original_labels = np.array([-1, -1, 0, 0, 0, 1, 1, -1, 0, 1])
    y_series = pd.Series(original_labels)
    
    logger.info(f"Original labels: {original_labels}")
    logger.info(f"Original distribution: {y_series.value_counts().to_dict()}")
    
    # Convert for PyTorch (should be 0, 1, 2)
    y_pytorch = y_series + 1
    
    logger.info(f"PyTorch labels: {y_pytorch.values}")
    logger.info(f"PyTorch distribution: {y_pytorch.value_counts().to_dict()}")
    
    # Validate ranges
    assert y_pytorch.min() >= 0, f"Invalid min label: {y_pytorch.min()}"
    assert y_pytorch.max() <= 2, f"Invalid max label: {y_pytorch.max()}"
    
    # Test conversion back
    y_converted_back = y_pytorch - 1
    
    logger.info(f"Converted back: {y_converted_back.values}")
    
    # Verify they match
    assert np.array_equal(original_labels, y_converted_back.values), "Conversion failed!"
    
    logger.info("✅ Label conversion test passed!")
    
    # Test edge cases
    logger.info("\nTesting edge cases...")
    
    # All SELL signals
    all_sell = pd.Series([-1, -1, -1, -1])
    all_sell_pytorch = all_sell + 1
    assert all_sell_pytorch.min() == 0 and all_sell_pytorch.max() == 0
    logger.info("✅ All SELL signals test passed")
    
    # All BUY signals  
    all_buy = pd.Series([1, 1, 1, 1])
    all_buy_pytorch = all_buy + 1
    assert all_buy_pytorch.min() == 2 and all_buy_pytorch.max() == 2
    logger.info("✅ All BUY signals test passed")
    
    # All WAIT signals
    all_wait = pd.Series([0, 0, 0, 0])
    all_wait_pytorch = all_wait + 1
    assert all_wait_pytorch.min() == 1 and all_wait_pytorch.max() == 1
    logger.info("✅ All WAIT signals test passed")
    
    logger.info("\n🎉 All label conversion tests passed!")

def test_pytorch_compatibility():
    """Test PyTorch tensor creation with converted labels"""
    try:
        import torch
        
        logger.info("\nTesting PyTorch tensor creation...")
        
        # Create sample data
        labels = pd.Series([-1, 0, 1, -1, 0, 1])
        labels_pytorch = labels + 1
        
        # Create tensor
        y_tensor = torch.LongTensor(labels_pytorch.values)
        
        logger.info(f"PyTorch tensor: {y_tensor}")
        logger.info(f"Tensor dtype: {y_tensor.dtype}")
        logger.info(f"Tensor shape: {y_tensor.shape}")
        
        # Test with CrossEntropyLoss (should not error)
        criterion = torch.nn.CrossEntropyLoss()
        
        # Create dummy predictions (3 classes)
        predictions = torch.randn(6, 3)  # 6 samples, 3 classes
        
        # This should not error
        loss = criterion(predictions, y_tensor)
        logger.info(f"CrossEntropyLoss computed successfully: {loss.item():.4f}")
        
        logger.info("✅ PyTorch compatibility test passed!")
        
    except ImportError:
        logger.warning("PyTorch not available, skipping compatibility test")
    except Exception as e:
        logger.error(f"PyTorch compatibility test failed: {e}")
        raise

if __name__ == "__main__":
    logger.info("="*60)
    logger.info("LABEL CONVERSION TEST")
    logger.info("="*60)
    
    test_label_conversion()
    test_pytorch_compatibility()
    
    logger.info("\n" + "="*60)
    logger.info("ALL TESTS COMPLETED SUCCESSFULLY!")
    logger.info("="*60)
