# Copy this file to .env and fill in your actual values
# NEVER commit the actual .env file to version control

# === API Keys ===
OPENAI_API_KEY=your_openai_api_key_here # Required: OpenAI API key
MARKETAUX_API_TOKEN=your_marketaux_token # Optional: MarketAux news API
TELEGRAM_TOKEN=your_telegram_bot_token # Optional: Telegram notifications
TELEGRAM_CHAT_ID=your_telegram_chat_id # Optional: Your Telegram chat ID
TELEGRAM_ENABLED=false

# === Hugging Face Configuration ===
HF_TOKEN=your_huggingface_token # Optional: HF token for private models and better rate limits
HF_CACHE_DIR=./cache/huggingface # Optional: Local cache directory for HF models
HF_OFFLINE_MODE=false # Use offline mode (cached models only)
HF_RETRY_ATTEMPTS=3 # Number of retry attempts for model loading
HF_RETRY_DELAY=1.0 # Initial delay between retries (seconds)

# === MT5 Configuration ===
MT5_FILES_DIR=C:/Users/<USER>/AppData/Roaming/MetaQuotes/Terminal/YourTerminalID/MQL5/Files # Required: MT5 files directory
MT5_ACCOUNT=12345 # Optional: Default account
MT5_PASSWORD=your_password # Optional: Account password
MT5_SERVER=YourBroker-Server # Optional: MT5 server

# === Trading Configuration ===
POSITION_TRADING_MODE=false # Enable position trading
TRADING_TIMEFRAMES=["H1", "H4"] # Default timeframes (for position trading: ["D1", "W1"])
TRADING_SYMBOLS=EURUSD,GBPUSD,USDCAD,AUDUSD,XAUUSD,USOIL.cash
SYMBOL_PROCESSING_DELAY=2.0 # Delay between symbols

# === Risk Management ===
RISK_PER_TRADE=1.0 # Risk % per trade
MAX_DAILY_TRADES=5 # Maximum trades per day
MAX_CONCURRENT_TRADES=3 # Maximum open positions
PORTFOLIO_HEAT_LIMIT=6.0 # Maximum portfolio risk %
TRADING_RISK_PROFILE=prop_firm # Risk profile: conservative, moderate, aggressive, prop_firm
TRADING_MAX_DAILY_DRAWDOWN_PERCENT=5.0 # Max 5% daily drawdown
TRADING_MAX_TOTAL_DRAWDOWN_PERCENT=10.0 # Max 10% total drawdown
TRADING_MAX_LOSS_PER_TRADE_PERCENT=5.0 # Emergency stop at 5% loss per trade
TRADING_EMERGENCY_STOP_ENABLED=true # Enable emergency stops
TRADING_TRAILING_STOP_ENABLED=true # Enable trailing stops

# === Trading Council Configuration ===
COUNCIL_CONFIDENCE_THRESHOLD=75.0 # Minimum confidence to trade
COUNCIL_QUICK_MODE=false # Skip debates for speed
COUNCIL_LLM_WEIGHT=0.7 # LLM vs ML weight (0-1)
COUNCIL_ML_WEIGHT=0.3 # ML contribution
TRADING_COUNCIL_AGENT_DELAY=0.75 # Delay between agents
TRADING_COUNCIL_DEBATE_ROUNDS=1 # Minimal debates
TRADING_COUNCIL_MIN_CONFIDENCE=50.0 # Production threshold

# === ML Configuration ===
ML_ENABLED=true # Enable ML predictions
ML_CONFIDENCE_THRESHOLD=0.7 # ML confidence threshold
ML_MODELS_PATH=./models # Path to ML models

# === GPU Training Configuration ===
ML_USE_GPU=true # Enable GPU acceleration for ML training
ML_TRAINING_DEVICE=auto # Device for training: auto, cpu, cuda
ML_TRAINING_START_YEAR=2021 # Start year for training data (uses all data from this year onwards)
ML_PYTORCH_ENABLED=true # Enable PyTorch neural networks
ML_LIGHTGBM_GPU=true # Enable LightGBM GPU acceleration

# === OpenAI Configuration ===
OPENAI_TIER=tier_1 # Your OpenAI tier
GPT_MODEL=gpt-4o-mini # Most cost-effective model
GPT_TEMPERATURE=0.1 # Low temperature for consistency

# === Backtesting ===
BACKTEST_MODE=auto # auto, full_council, simplified, ml_only
BACKTEST_USE_CHEAP_MODELS=true # Use GPT-3.5 for backtesting
BACKTEST_CACHE_ENABLED=true # Cache backtesting results

# === Performance & Optimization ===
CACHE_ENABLED=true # Enable market state caching
CACHE_TTL_MINUTES=60 # Cache validity period
CACHE_SIMILARITY_THRESHOLD=0.85 # Similarity for cache hits
PRE_FILTER_ENABLED=true # Enable pre-trade filtering
OFFLINE_VALIDATION_ENABLED=true # Enable offline validation
OFFLINE_VALIDATION_THRESHOLD=60 # Minimum validation score

# === News & Sentiment ===
NEWS_ENABLED=true # Enable news filtering
NEWS_BLACKOUT_MINUTES=30 # Blackout before/after news
MARKETAUX_ENABLED=true
MARKETAUX_SENTIMENT_WEIGHT=0.3

# === Rate Limiting & Error Handling ===
ENABLE_RATE_LIMITING=true
RATE_LIMIT_SAFETY_MARGIN=0.8
MAX_RETRIES=3
CIRCUIT_BREAKER_ENABLED=true
CIRCUIT_BREAKER_THRESHOLD=5
CIRCUIT_BREAKER_TIMEOUT=300 # 5 minutes

# === Logging & Monitoring ===
LOG_LEVEL=INFO # DEBUG, INFO, WARNING, ERROR
LOG_TO_FILE=true # Log to files
LOG_FILE_PATH=./logs # Log directory
ENABLE_TELEGRAM_LOGS=false # Send logs to Telegram
LOG_GPT_REQUESTS=true
TRACK_TOKEN_COSTS=true
ALERT_ON_ERRORS=true

# MarketAux News API Configuration (OPTIONAL)
MARKETAUX_DAILY_LIMIT=100
MARKETAUX_REQUESTS_PER_MINUTE=5
MARKETAUX_CACHE_TTL_HOURS=24
MARKETAUX_MIN_RELEVANCE_SCORE=0.3
MARKETAUX_HIGH_IMPACT_ONLY=false

# Trading Configuration
TRADING_OFFLINE_VALIDATION_THRESHOLD=0.3

# Cache Configuration
TRADING_CACHE_ENABLED=true
TRADING_CACHE_SIMILARITY_THRESHOLD=0.85
TRADING_CACHE_TTL_MINUTES=60
TRADING_CACHE_SIZE_MB=500

# Production Settings
TRADING_COUNCIL_QUICK_MODE=false
TRADING_SYMBOL_PROCESSING_DELAY=2.0
