# Environment Setup Guide

## Overview

This guide helps you configure all environment variables for the GPT Trading System, including the new GPU training and Hugging Face integration.

## Quick Setup

1. **Copy the example file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` with your actual values**

3. **Run the setup verification:**
   ```bash
   python scripts/test_gpu_setup.py
   ```

## Required Variables

### OpenAI API Key (REQUIRED)
```bash
OPENAI_API_KEY=sk-proj-your-actual-openai-key-here
```
- Get from: https://platform.openai.com/api-keys
- Required for GPT trading decisions

### MT5 Configuration (REQUIRED)
```bash
MT5_FILES_DIR=C:/Users/<USER>/AppData/Roaming/MetaQuotes/Terminal/YourTerminalID/MQL5/Files
```

**How to find your MT5 Files Directory:**
1. Open MetaTrader 5
2. Go to **File → Open Data Folder**
3. Navigate to **MQL5/Files**
4. Copy the full path

**Example paths:**
- `C:/Users/<USER>/AppData/Roaming/MetaQuotes/Terminal/D0E8209F77C8CF37AD8BF550E51FF075/MQL5/Files`
- `C:/Users/<USER>/AppData/Roaming/MetaQuotes/Terminal/A1B2C3D4E5F6G7H8/MQL5/Files`

## Optional but Recommended Variables

### Hugging Face Token
```bash
HF_TOKEN=hf_your_hugging_face_token_here
```
- Get from: https://huggingface.co/settings/tokens
- **Benefits:**
  - Access to private models
  - Higher rate limits
  - Better download speeds
  - Required for some advanced features

**How to get HF Token:**
1. Go to https://huggingface.co/settings/tokens
2. Create a new token with "Read" permissions
3. Copy and paste into your `.env` file

### GPU Training Configuration
```bash
ML_USE_GPU=true
ML_TRAINING_DEVICE=auto
ML_TRAINING_PERIOD_YEARS=3
ML_PYTORCH_ENABLED=true
ML_LIGHTGBM_GPU=true
```

### Telegram Notifications
```bash
TELEGRAM_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
TELEGRAM_ENABLED=true
```

**How to setup Telegram:**
1. Create a bot: Message @BotFather on Telegram
2. Get your chat ID: Message @userinfobot
3. Add both to your `.env` file

## Complete .env Template

```bash
# === API Keys ===
OPENAI_API_KEY=sk-proj-your-actual-openai-key-here
MARKETAUX_API_TOKEN=your_marketaux_token
TELEGRAM_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
TELEGRAM_ENABLED=false

# === Hugging Face Configuration ===
HF_TOKEN=hf_your_hugging_face_token_here
HF_CACHE_DIR=./cache/huggingface
HF_OFFLINE_MODE=false
HF_RETRY_ATTEMPTS=3
HF_RETRY_DELAY=1.0

# === MT5 Configuration ===
MT5_FILES_DIR=C:/Users/<USER>/AppData/Roaming/MetaQuotes/Terminal/YourTerminalID/MQL5/Files
MT5_ACCOUNT=12345
MT5_PASSWORD=your_password
MT5_SERVER=YourBroker-Server

# === Trading Configuration ===
TRADING_SYMBOLS=EURUSD,GBPUSD,USDCAD,AUDUSD,XAUUSD,USOIL.cash
RISK_PER_TRADE=1.0
MAX_CONCURRENT_TRADES=3
COUNCIL_CONFIDENCE_THRESHOLD=75.0

# === ML & GPU Configuration ===
ML_ENABLED=true
ML_USE_GPU=true
ML_TRAINING_PERIOD_YEARS=3
ML_PYTORCH_ENABLED=true
ML_LIGHTGBM_GPU=true

# === Performance Settings ===
GPT_MODEL=gpt-4o-mini
CACHE_ENABLED=true
LOG_LEVEL=INFO
```

## Verification

### Test Your Setup
```bash
# Test GPU capabilities
python scripts/test_gpu_setup.py

# Test full configuration
python scripts/train_ml_production.py
```

### Expected Output
```
✅ GPU SETUP SUCCESSFUL!
Your NVIDIA GeForce RTX 3090 Ti is ready for ML training!
```

## Troubleshooting

### Common Issues

#### 1. MT5_FILES_DIR Not Found
```
ValidationError: MT5Settings files_dir Field required
```
**Solution:** Set the correct MT5 files directory path

#### 2. Hugging Face Authentication Error
```
401 Unauthorized - Authentication error detected
```
**Solution:** Set `HF_TOKEN` with a valid Hugging Face token

#### 3. GPU Not Detected
```
CUDA available: False
```
**Solutions:**
- Install NVIDIA drivers
- Install CUDA toolkit
- Run `nvidia-smi` to verify GPU

#### 4. PyTorch CUDA Issues
```
PyTorch not available or CUDA not available
```
**Solution:** Install PyTorch with CUDA:
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### Path Format Issues

**Windows paths should use forward slashes or double backslashes:**
```bash
# ✅ Correct
MT5_FILES_DIR=C:/Users/<USER>/AppData/Roaming/MetaQuotes/Terminal/ABC123/MQL5/Files

# ✅ Also correct
MT5_FILES_DIR=C:\\Users\\<USER>\\AppData\\Roaming\\MetaQuotes\\Terminal\\ABC123\\MQL5\\Files

# ❌ Wrong (single backslashes)
MT5_FILES_DIR=C:\Users\<USER>\AppData\Roaming\MetaQuotes\Terminal\ABC123\MQL5\Files
```

## Security Notes

1. **Never commit `.env` to version control**
2. **Keep API keys secure**
3. **Use environment-specific configurations**
4. **Regularly rotate API keys**

## Advanced Configuration

### Custom Training Periods
```bash
# Use 5 years of data (if available from your broker)
ML_TRAINING_PERIOD_YEARS=5

# Use 1 year for faster training
ML_TRAINING_PERIOD_YEARS=1
```

### GPU Memory Management
```bash
# Limit GPU memory usage
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Force CPU training
CUDA_VISIBLE_DEVICES=""
```

### Hugging Face Offline Mode
```bash
# Use only cached models (no internet required)
HF_OFFLINE_MODE=true
HF_CACHE_DIR=./cache/huggingface
```

## Next Steps

After configuring your `.env` file:

1. **Test the setup:** `python scripts/test_gpu_setup.py`
2. **Train ML models:** `python scripts/train_ml_production.py`
3. **Start trading:** `python trading_loop.py`

Your system is now configured for optimal GPU-accelerated ML training with 3 years of historical data!
