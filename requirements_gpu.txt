# GPU-accelerated ML training requirements for GPT Trading System

# Core ML libraries
scikit-learn>=1.3.0
lightgbm>=4.0.0
joblib>=1.3.0
pandas>=2.0.0
numpy>=1.24.0

# PyTorch for GPU neural networks
# For CUDA 11.8 (most common)
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# Alternative PyTorch installation commands:
# For CUDA 11.8: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
# For CUDA 12.1: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
# For CPU only: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# Multi-threaded Gradient Boosting Libraries
xgboost>=1.7.0  # Fast multi-threaded gradient boosting with optional GPU support
catboost>=1.2.0  # Fast multi-threaded gradient boosting with GPU support

# Performance monitoring
psutil>=5.9.0
GPUtil>=1.4.0  # For GPU monitoring

# Hugging Face optimizations
hf_xet>=0.16.0  # Faster Hugging Face model downloads

# Note: For LightGBM GPU support, you may need to install with:
# pip install lightgbm --config-settings=cmake.define.USE_GPU=ON
